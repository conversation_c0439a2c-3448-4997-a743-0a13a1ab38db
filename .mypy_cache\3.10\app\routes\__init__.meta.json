{"data_mtime": 1757726484, "dep_lines": [3, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 15, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["app.routes.export_summary", "app.routes.etl_api", "app.routes.upload_page", "app.routes.customer_summary_db", "app.routes.order_summary_db", "app.routes.get_order_details_db", "app.routes.delete_order_db", "app.routes.filter_orders_db", "app.routes.filter_data_db", "app.routes.filter_overdue_orders_db", "app.routes.summary_data_db", "app.routes.overdue_summary", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b31ded3249de7e08e5847f2b195c9cf8d836e035", "id": "app.routes", "ignore_all": true, "interface_hash": "8216b388d4dfb4eac135b05f86799e79aed612f2", "mtime": 1757415189, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\app\\routes\\__init__.py", "plugin_data": null, "size": 1470, "suppressed": [], "version_id": "1.15.0"}