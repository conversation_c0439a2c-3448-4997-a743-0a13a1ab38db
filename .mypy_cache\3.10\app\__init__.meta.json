{"data_mtime": 1757726484, "dep_lines": [5, 4, 6, 3, 7, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["app.utils.logging_config", "app.config", "app.routes", "flask", "logging", "secrets", "os", "scheduler", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "a533ffa8e31809d8455059019139cd3dc3c8a29d", "id": "app", "ignore_all": true, "interface_hash": "51f83315ee10f2269a3ae2361b746ae5fa5700ec", "mtime": 1757415189, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\app\\__init__.py", "plugin_data": null, "size": 3287, "suppressed": [], "version_id": "1.15.0"}