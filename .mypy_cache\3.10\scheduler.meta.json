{"data_mtime": 1757421512, "dep_lines": [9, 4, 5, 6, 8, 10, 16, 1, 1, 1, 1, 1, 1, 1, 7], "dep_prios": [5, 10, 10, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["sqlalchemy.orm", "sys", "os", "logging", "sqlalchemy", "datetime", "etl", "builtins", "_frozen_importlib", "_typeshed", "abc", "genericpath", "posixpath", "typing"], "hash": "37c163440e85c92a7201bc7011869cd3b68dee4d", "id": "scheduler", "ignore_all": true, "interface_hash": "b75e43e6e8d80303b35b10445b5016f15e3656dc", "mtime": 1757415189, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\scheduler.py", "plugin_data": null, "size": 3943, "suppressed": ["flask_apscheduler"], "version_id": "1.15.0"}