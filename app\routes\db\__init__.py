# app/routes/db/__init__.py
# 数据库访问层初始化

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, scoped_session
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()
DB_URI = os.getenv('DATABASE_URI', 'postgresql://flask_user:flask_password@localhost:5433/flask_db')  # 默认 PostgreSQL 数据库

# 创建数据库引擎（连接池与探活）
# 关键点：
# - pool_pre_ping: 在取用连接前探活，避免使用断开的连接
# - pool_recycle: 定期回收连接，防止长时间空闲被中间设备断开
# - connect_args: 开启TCP keepalive，进一步降低断开概率
pool_recycle = int(os.getenv('DB_POOL_RECYCLE', '300'))
pool_timeout = int(os.getenv('DB_POOL_TIMEOUT', '20'))
pool_size = int(os.getenv('DB_POOL_SIZE', '5'))
max_overflow = int(os.getenv('DB_MAX_OVERFLOW', '10'))

engine = create_engine(
    DB_URI,
    echo=False,
    pool_pre_ping=True,
    pool_recycle=pool_recycle,
    pool_timeout=pool_timeout,
    pool_size=pool_size,
    max_overflow=max_overflow,
    connect_args={
        "keepalives": 1,
        "keepalives_idle": int(os.getenv('PG_KEEPALIVES_IDLE', '60')),
        "keepalives_interval": int(os.getenv('PG_KEEPALIVES_INTERVAL', '30')),
        "keepalives_count": int(os.getenv('PG_KEEPALIVES_COUNT', '5')),
    },
)

# 创建会话工厂
session_factory = sessionmaker(bind=engine)

# 创建线程安全的会话
Session = scoped_session(session_factory)

def get_db_session():
    """获取数据库会话"""
    try:
        # 依赖 pool_pre_ping 探活，无需多余的 SELECT 1
        return Session()
    except Exception as e:
        print(f"数据库连接失败: {e}")
        # scoped_session 无需显式关闭未创建成功的会话
        raise

def close_db_session(session):
    """关闭数据库会话"""
    # 对 scoped_session，使用 remove() 清理线程本地状态
    try:
        if session:
            session.close()
    finally:
        Session.remove()
