import os
import json
import glob
from datetime import datetime
from typing import Optional, Tuple


REPORT_DIR = os.path.join(os.getcwd(), 'reports')


def ensure_dir(path: str) -> None:
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)


def make_run_id() -> str:
    # Use UTC timestamp compact run id
    return datetime.utcnow().strftime('%Y%m%dT%H%M%S%fZ')


def report_path_for(run_id: str) -> str:
    ensure_dir(REPORT_DIR)
    return os.path.join(REPORT_DIR, f'import_report_{run_id}.json')


def save_report(run_id: str, report: dict) -> str:
    path = report_path_for(run_id)
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    return path


def load_report(run_id: str) -> Optional[dict]:
    path = report_path_for(run_id)
    if not os.path.exists(path):
        return None
    with open(path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_latest_report() -> Optional[Tuple[str, dict]]:
    ensure_dir(REPORT_DIR)
    files = sorted(glob.glob(os.path.join(REPORT_DIR, 'import_report_*.json')))
    if not files:
        return None
    latest = files[-1]
    run_id = os.path.basename(latest).replace('import_report_', '').replace('.json', '')
    with open(latest, 'r', encoding='utf-8') as f:
        return run_id, json.load(f)

