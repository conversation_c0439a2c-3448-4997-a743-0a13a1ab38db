# run_migrations.py
# 执行数据库索引优化迁移脚本

import logging
import os
import sys
from sqlalchemy import create_engine
from app.routes.db.migrations.add_performance_indexes import run_migration as run_indexes
from app.routes.db.migrations.add_delta_amount_column import run_migration as run_delta
from app.routes.db.migrations.add_period_num_int_column import run_migration as run_period_num
from app.routes.db.migrations.add_auto_inferred_column import run_migration as run_auto_inferred

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migrations.log')
    ]
)
logger = logging.getLogger(__name__)

# 从环境变量获取数据库URI，如果没有则使用默认值
DATABASE_URI = os.environ.get(
    'DATABASE_URI', 
    'postgresql://flask_user:flask_password@localhost:5432/flask_db'
)

def main():
    """
    主函数 - 运行迁移脚本
    """
    logger.info("开始执行数据库索引优化迁移")
    
    try:
        # 创建数据库连接引擎
        logger.info(f"连接数据库：{DATABASE_URI}")
        engine = create_engine(DATABASE_URI)
        
        # 运行迁移（顺序：列 -> 列 -> 索引）
        ok_delta = run_delta(engine)
        ok_p_auto = run_auto_inferred(engine)
        ok_period = run_period_num(engine)
        ok_idx = run_indexes(engine)
        if ok_delta and ok_p_auto and ok_period and ok_idx:
            logger.info("数据库迁移全部成功完成！")
            return 0
        logger.error("部分迁移失败，请检查日志。")
        return 1
            
    except Exception as e:
        logger.exception(f"执行迁移时发生错误：{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
