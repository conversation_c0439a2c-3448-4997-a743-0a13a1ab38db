#!/usr/bin/env python3
"""
Performance Testing Script for ETL Optimizations

This script tests the performance improvements of the optimized ETL functions
and validates that business logic produces identical results.
"""

import time
import logging
import os
import sys
import json
from datetime import datetime
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker

# Add project root to path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

from etl import (
    get_session, DB_URI, 
    update_payment_status_and_receivable,
    update_financial_fields,
    update_order_status,
    run_etl
)
from app.routes.db.models import Order, Transaction, PaymentSchedule

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('performance_test.log')
    ]
)
logger = logging.getLogger(__name__)

class PerformanceTestSuite:
    """Performance testing suite for ETL optimizations"""
    
    def __init__(self):
        self.engine = create_engine(DB_URI)
        self.Session = sessionmaker(bind=self.engine)
        self.results = {}
        
    def get_session(self):
        """Get database session"""
        return self.Session()
    
    def capture_database_state(self, session, test_name):
        """Capture current database state for comparison"""
        state = {
            'timestamp': datetime.now().isoformat(),
            'test_name': test_name,
            'orders': {
                'count': session.query(Order).count(),
                'total_receivable_sum': session.query(func.sum(Order.total_receivable)).scalar() or 0,
                'current_receivable_sum': session.query(func.sum(Order.current_receivable)).scalar() or 0,
                'cost_sum': session.query(func.sum(Order.cost)).scalar() or 0,
                'repaid_amount_sum': session.query(func.sum(Order.repaid_amount)).scalar() or 0,
                'overdue_principal_sum': session.query(func.sum(Order.overdue_principal)).scalar() or 0,
            },
            'transactions': {
                'count': session.query(Transaction).count(),
                'amount_sum': session.query(func.sum(Transaction.amount)).scalar() or 0,
            },
            'payment_schedules': {
                'count': session.query(PaymentSchedule).count(),
                'amount_sum': session.query(func.sum(PaymentSchedule.amount)).scalar() or 0,
                'paid_amount_sum': session.query(func.sum(PaymentSchedule.paid_amount)).scalar() or 0,
            }
        }
        return state
    
    def compare_states(self, before_state, after_state):
        """Compare two database states for differences"""
        differences = []
        
        for table in ['orders', 'transactions', 'payment_schedules']:
            for metric in before_state[table]:
                before_val = before_state[table][metric]
                after_val = after_state[table][metric]
                
                if abs(before_val - after_val) > 0.01:  # Allow for small floating point differences
                    differences.append({
                        'table': table,
                        'metric': metric,
                        'before': before_val,
                        'after': after_val,
                        'difference': after_val - before_val
                    })
        
        return differences
    
    def measure_function_performance(self, func, session, *args, **kwargs):
        """Measure execution time and database queries for a function"""
        # Capture initial state
        initial_state = self.capture_database_state(session, f"{func.__name__}_before")
        
        # Measure execution time
        start_time = time.time()
        start_cpu = time.process_time()
        
        try:
            result = func(session, *args, **kwargs)
            success = True
            error = None
        except Exception as e:
            result = None
            success = False
            error = str(e)
            logger.error(f"Function {func.__name__} failed: {e}")
        
        end_time = time.time()
        end_cpu = time.process_time()
        
        # Capture final state
        final_state = self.capture_database_state(session, f"{func.__name__}_after")
        
        # Calculate metrics
        wall_time = end_time - start_time
        cpu_time = end_cpu - start_cpu
        
        # Check for state differences (should be minimal for update functions)
        differences = self.compare_states(initial_state, final_state)
        
        performance_data = {
            'function_name': func.__name__,
            'wall_time': wall_time,
            'cpu_time': cpu_time,
            'success': success,
            'error': error,
            'initial_state': initial_state,
            'final_state': final_state,
            'state_differences': differences,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"{func.__name__} - Wall time: {wall_time:.4f}s, CPU time: {cpu_time:.4f}s, Success: {success}")
        
        return performance_data
    
    def test_financial_field_updates(self):
        """Test performance of financial field update functions"""
        logger.info("Testing financial field update performance...")
        
        session = self.get_session()
        try:
            # Test update_financial_fields
            financial_perf = self.measure_function_performance(
                update_financial_fields, session
            )
            self.results['update_financial_fields'] = financial_perf
            
            # Test update_payment_status_and_receivable
            payment_perf = self.measure_function_performance(
                update_payment_status_and_receivable, session
            )
            self.results['update_payment_status_and_receivable'] = payment_perf
            
            # Test update_order_status
            order_status_perf = self.measure_function_performance(
                update_order_status, session
            )
            self.results['update_order_status'] = order_status_perf
            
        finally:
            session.close()
    
    def test_data_consistency(self):
        """Test that optimized functions produce consistent results"""
        logger.info("Testing data consistency...")
        
        session = self.get_session()
        try:
            # Run functions multiple times and check for consistency
            states = []
            for i in range(3):
                logger.info(f"Consistency test run {i+1}/3")
                
                # Run all update functions
                update_financial_fields(session)
                update_payment_status_and_receivable(session)
                update_order_status(session)
                
                # Capture state
                state = self.capture_database_state(session, f"consistency_run_{i+1}")
                states.append(state)
            
            # Compare states for consistency
            consistency_results = []
            for i in range(1, len(states)):
                differences = self.compare_states(states[0], states[i])
                consistency_results.append({
                    'run_comparison': f"run_1_vs_run_{i+1}",
                    'differences': differences,
                    'is_consistent': len(differences) == 0
                })
            
            self.results['consistency_test'] = {
                'states': states,
                'comparisons': consistency_results,
                'overall_consistent': all(r['is_consistent'] for r in consistency_results)
            }
            
        finally:
            session.close()
    
    def generate_report(self):
        """Generate performance test report"""
        report = {
            'test_summary': {
                'timestamp': datetime.now().isoformat(),
                'total_tests': len(self.results),
                'database_uri': DB_URI
            },
            'performance_results': self.results,
            'recommendations': []
        }
        
        # Add performance recommendations
        if 'update_financial_fields' in self.results:
            wall_time = self.results['update_financial_fields']['wall_time']
            if wall_time > 10:
                report['recommendations'].append(
                    f"update_financial_fields took {wall_time:.2f}s - consider further optimization"
                )
            else:
                report['recommendations'].append(
                    f"update_financial_fields performance is good: {wall_time:.2f}s"
                )
        
        # Save report
        report_file = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Performance report saved to {report_file}")
        return report
    
    def run_all_tests(self):
        """Run all performance tests"""
        logger.info("Starting comprehensive performance test suite...")
        
        try:
            # Test individual function performance
            self.test_financial_field_updates()
            
            # Test data consistency
            self.test_data_consistency()
            
            # Generate report
            report = self.generate_report()
            
            logger.info("Performance test suite completed successfully")
            return report
            
        except Exception as e:
            logger.error(f"Performance test suite failed: {e}")
            raise

def main():
    """Main function to run performance tests"""
    logger.info("ETL Performance Test Suite Starting...")
    
    test_suite = PerformanceTestSuite()
    report = test_suite.run_all_tests()
    
    # Print summary
    print("\n" + "="*80)
    print("PERFORMANCE TEST SUMMARY")
    print("="*80)
    
    for test_name, result in report['performance_results'].items():
        if 'wall_time' in result:
            print(f"{test_name}: {result['wall_time']:.4f}s (Success: {result['success']})")
    
    print("\nRecommendations:")
    for rec in report['recommendations']:
        print(f"- {rec}")
    
    print("="*80)

if __name__ == "__main__":
    main()
