# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Running the Application
- **Development**: `python run.py` (runs on host='0.0.0.0', port=5000)
- **Alternative Dev**: `python start_dev.py` (development server with auto-reload)
- **Production**: `gunicorn -w 4 -b 0.0.0.0:5000 --timeout 300 --worker-class gthread --threads 2 --max-requests 1000 --max-requests-jitter 100 "run:app"`
- **Health Check**: Access `/health` endpoint to verify service status

### Database Operations
- **ETL Data Import**: `python etl.py` (imports Excel data to database)
- **Run Migrations**: `python run_migrations.py`
- **Test Scheduler**: `python scheduler.py` (runs payment status update job once)
- **Database Migration**: `python migrate_to_postgresql.py` (migrates from SQLite to PostgreSQL)
- **Database Management**: `python db_management.py [init|backup|restore|status]`
- **Database Connection Test**: `python diagnose_connection.py`
- **Database Confirmation**: `python confirm_database.py`

### Utility Scripts
- **Demo Lease Validation**: `python scripts/demo_validate_new_lease.py` (validate new lease applications)
- **Demo Import Report**: `python scripts/demo_import_report.py`

### Docker Operations
- **Local Development**: `docker-compose -f docker-compose.dev.yml up`
- **Production**: `docker-compose up`

### Dependency Management
- **Install Dependencies**: `pip install -r requirements.txt`
- **Environment Setup**: Copy `env.example` to `.env` and configure

## Architecture Overview

### Application Structure
This is a Flask-based order management and financial tracking system with PostgreSQL as the primary database:

1. **PostgreSQL Database**: Primary data persistence with full ORM models and enterprise-grade performance
2. **Excel Integration**: Excel data processing and import capabilities via ETL pipeline
3. **Service-Oriented Architecture**: Domain-separated services for business logic

### Core Components

#### Data Layer Architecture
- **app/routes/db/models.py**: SQLAlchemy ORM models (Order, PaymentSchedule, Transaction, CustomerInfo)
- **etl.py**: ETL pipeline that syncs Excel data to PostgreSQL database
- **migrate_to_postgresql.py**: Database migration tool for SQLite to PostgreSQL transition
- **db_management.py**: PostgreSQL database management utilities (backup, restore, status)

#### Service Layer (Modern Architecture)
- **app/services/**: Business logic layer with domain separation
  - **summary_service.py**: Main business service coordinating data access and calculations
  - **summary_repository.py**: Data access layer for summary operations
  - **summary_calculator.py**: Business calculation logic
  - **dto.py**: Data transfer objects for service layer communication
  - **config.py**: Service-specific configuration and constants

#### Route Organization
Routes are organized by functionality in `app/routes/`:
- **Database Routes**: SQLAlchemy-based operations (suffixed with `_db.py`)
- **API Routes**: RESTful endpoints for data access
- **Blueprint Registration**: All routes auto-registered via `app/routes/__init__.py`

#### Authentication & Security
- **API Key Authentication**: `app/auth/decorators.py` - `@require_api_key` decorator
- **Session Management**: Flask sessions with filesystem storage
- **Configuration**: Environment-based config in `app/config.py`

#### Scheduled Tasks
- **Scheduler**: `scheduler.py` uses flask-apscheduler
- **Daily Tasks**: Payment status updates, financial calculations, index optimization
- **Execution**: Daily at 1:00 AM Asia/Shanghai timezone
- **Background Jobs**: Payment status updater and financial field calculations

#### Utilities and Infrastructure
- **Timezone Support**: `app/utils/timezone_logger.py` for proper time zone handling
- **Date Parsing**: `app/utils/date_parser.py` handles various date formats
- **Logging Config**: `app/utils/logging_config.py` centralized logging setup
- **Session Management**: `app/utils/session_manager.py` for user session handling
- **Lease Rules**: `app/utils/lease_rules.py` for business rule validation

### Key Patterns

#### Database Design
- **Performance Optimized**: Extensive indexing strategy for common queries (see models.py:58-97)
- **Relational Integrity**: Foreign key relationships with cascade operations
- **Financial Calculations**: Automated calculation of repaid amounts, overdue principals
- **Complex Indexes**: Multi-column indexes for date+shop, status queries

#### Service Architecture Pattern
- **Repository Pattern**: Data access abstraction in summary_repository.py
- **Service Layer**: Business logic coordination in summary_service.py
- **DTO Pattern**: Structured data transfer with validation
- **Separation of Concerns**: Clear boundaries between data, business logic, and presentation

#### Route Patterns
- Blueprint-based organization with consistent naming
- Database-first approach with PostgreSQL as primary data store
- Standardized error handling and JSON response formats

### Environment Configuration
- **Development**: DEBUG=True, PostgreSQL on port 5433 to avoid conflicts
- **Production**: DEBUG=False, PostgreSQL on port 5432
- **Database URI**: Configurable via `DATABASE_URI` environment variable
- **Secret Key Persistence**: Automatic generation and persistence via instance folder

### Deployment Strategy
- **Multi-platform Docker Support**: ARM64 and AMD64 architectures
- **Container Orchestration**: Docker Compose with PostgreSQL service
- **Health Monitoring**: Built-in health check endpoint and PostgreSQL health checks
- **Volume Persistence**: Data, logs, and instance folders mounted as volumes

## PostgreSQL Database Configuration

### Database Connection
- **Production**: `****************************************************/flask_db`
- **Development**: `postgresql://flask_user:flask_password@localhost:5433/flask_db` (port 5433 to avoid conflicts)
- **Configuration**: Set `DATABASE_URI` environment variable

### Docker Deployment
- **Production**: `docker-compose up` (includes PostgreSQL service on port 5432)
- **Development**: `docker-compose -f docker-compose.dev.yml up` (PostgreSQL on port 5433)

### Database Management Commands
```bash
# Initialize database (create tables and indexes)
python db_management.py init

# Backup database
python db_management.py backup

# Restore database from backup
python db_management.py restore backup_file.sql

# Check database status
python db_management.py status

# Migrate from SQLite to PostgreSQL
python migrate_to_postgresql.py
```

### Migration from SQLite
1. **Backup current SQLite data** (optional but recommended)
2. **Set up PostgreSQL** using Docker compose files
3. **Run migration script**: `python migrate_to_postgresql.py`
4. **Verify migration**: `python db_management.py status`
5. **Update environment variables** to use PostgreSQL URI

### Performance Optimizations
- **Connection Pooling**: Configured with pool_pre_ping and pool_recycle
- **Indexes**: Automated creation of performance indexes during migration
- **Health Checks**: PostgreSQL container includes built-in health monitoring
- **Data Persistence**: Docker volumes ensure data persistence across container restarts