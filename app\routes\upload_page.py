# app/routes/upload_page.py

from flask import Blueprint, render_template, redirect, url_for, session
from app.routes.auth import login_required

# 创建蓝图
bp = Blueprint('upload_page', __name__)

@bp.route('/upload', methods=['GET'])
def index():
    """
    渲染Excel上传页面
    """
    # 检查用户是否已登录
    if not session.get('logged_in'):
        return redirect(url_for('auth.login_page'))
    return render_template('upload.html')
