from datetime import date
import importlib.util
import os


def _load_lease_rules():
    here = os.path.dirname(os.path.abspath(__file__))
    target = os.path.abspath(os.path.join(here, '..', 'app', 'utils', 'lease_rules.py'))
    spec = importlib.util.spec_from_file_location("lease_rules", target)
    mod = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mod)  # type: ignore
    return mod


lr = _load_lease_rules()
compute_expected_amounts = lr.compute_expected_amounts
infer_p56_dates = lr.infer_p56_dates


def show_case(title, product_type, model, periods, devices, rent, dp, buyout, base_date):
    amounts, meta = compute_expected_amounts(product_type, model, periods, devices, rent, dp, buyout)
    print(f"=== {title} ===")
    print(f"product_type={product_type}, model={model}, periods={periods}, devices={devices}")
    print(f"inputs: rent={rent}, dp={dp}, buyout={buyout}")
    print(f"meta: {meta}")
    ks = sorted(amounts.keys())
    for k in ks:
        print(f"期 {k}: 应还 {amounts[k]:.2f}")
    if 4 in ks and base_date is not None and periods == 4:
        dates = infer_p56_dates(base_date)
        print(f"推导日期: 第5期={dates[5]}, 第6期={dates[6]}")
    print()


def main():
    # 新租赁 4+2，示例（pricing.csv 将被用于估算 rent/dp/buyout，如未提供）
    show_case(
        title="新租赁4+2（依赖pricing.csv）",
        product_type="租赁",
        model="iPhone16 promax 512G",
        periods=4,
        devices=1,
        rent=None,  # 让脚本从定价表估算
        dp=None,
        buyout=None,
        base_date=date(2025, 1, 15)
    )

    # 旧租赁 6期，传入明确的每期租金与买断金
    show_case(
        title="旧租赁6期（显式传入租金/买断）",
        product_type="租赁",
        model="iPhone16 Pro Max 512G",
        periods=6,
        devices=1,
        rent=2274.0,
        dp=610.0,
        buyout=116.0,
        base_date=None
    )

    # 电商示例
    show_case(
        title="电商（统一每期金额）",
        product_type="电商",
        model="iPhone16 Pro Max 512G",
        periods=6,
        devices=1,
        rent=1979.9,
        dp=119.6,
        buyout=0.0,
        base_date=None
    )


if __name__ == "__main__":
    main()
