{".class": "MypyFile", "_fullname": "app", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DevelopmentConfig": {".class": "SymbolTableNode", "cross_ref": "app.config.DevelopmentConfig", "kind": "Gdef"}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "ProductionConfig": {".class": "SymbolTableNode", "cross_ref": "app.config.ProductionConfig", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.create_app", "name": "create_app", "type": null}}, "init_scheduler": {".class": "SymbolTableNode", "cross_ref": "scheduler.init_scheduler", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "register_blueprints": {".class": "SymbolTableNode", "cross_ref": "app.routes.register_blueprints", "kind": "Gdef"}, "secrets": {".class": "SymbolTableNode", "cross_ref": "secrets", "kind": "Gdef"}, "setup_logging": {".class": "SymbolTableNode", "cross_ref": "app.utils.logging_config.setup_logging", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\app\\__init__.py"}