# 忽略 Python 编译文件
__pycache__/
*.pyc
*.pyo
*.pyd

# 忽略日志文件
*.log
logs/
reports/
.claude/

# 忽略系统文件
.DS_Store
Thumbs.db

# 忽略 IDE 配置文件
.vscode/
.idea/
*.swp
*.swo

# 忽略上传文件
uploads/

# 忽略虚拟环境
venv/
.venv/
.env

# 忽略其他系统生成的文件和目录
.trunk/
.cursorignore

# 保留的文件和目录（不忽略）
!app/
!run.py
!requirements.txt
!README.md

# 保留必要的 Excel 文件（如果这些是项目必需的）
!TTXW.xlsm
!TTXW内.xlsm
!TTXW外.xlsm

# 保留备份目录（如果这些是项目必需的）
!excel备份/ 
