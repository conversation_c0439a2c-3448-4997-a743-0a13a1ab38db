{".class": "MypyFile", "_fullname": "app.routes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.routes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.routes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.routes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.routes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.routes.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.routes.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.routes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "customer_summary_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.customer_summary_db.bp", "kind": "Gdef"}, "delete_order_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.delete_order_db.bp", "kind": "Gdef"}, "etl_api_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.etl_api.bp", "kind": "Gdef"}, "export_summary_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.export_summary.bp", "kind": "Gdef"}, "filter_data_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.filter_data_db.bp", "kind": "Gdef"}, "filter_orders_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.filter_orders_db.bp", "kind": "Gdef"}, "filter_overdue_orders_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.filter_overdue_orders_db.bp", "kind": "Gdef"}, "get_order_details_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.get_order_details_db.bp", "kind": "Gdef"}, "order_summary_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.order_summary_db.bp", "kind": "Gdef"}, "overdue_summary_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.overdue_summary.bp", "kind": "Gdef"}, "register_blueprints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.routes.register_blueprints", "name": "register_blueprints", "type": null}}, "summary_data_db_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.summary_data_db.bp", "kind": "Gdef"}, "upload_page_bp": {".class": "SymbolTableNode", "cross_ref": "app.routes.upload_page.bp", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\app\\routes\\__init__.py"}