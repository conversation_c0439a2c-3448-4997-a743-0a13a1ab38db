{"data_mtime": 1757513662, "dep_lines": [3, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["app", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "44b37b2441370f0bdfe03fed5b4b73803bc094a6", "id": "run", "ignore_all": false, "interface_hash": "1e00ac0e72de44a5c9c2652e520d9699547240dc", "mtime": 1757415189, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\run.py", "plugin_data": null, "size": 311, "suppressed": [], "version_id": "1.15.0"}