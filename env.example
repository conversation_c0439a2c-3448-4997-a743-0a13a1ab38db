# Flask应用配置
SECRET_KEY=your-super-secret-key-here
FLASK_ENV=production

# 数据库配置 - PostgreSQL (推荐)
DATABASE_URI=****************************************************/flask_db

# 本地开发使用端口5433（避免与本地PostgreSQL冲突）
# DATABASE_URI=postgresql://flask_user:flask_password@localhost:5433/flask_db

# 数据库配置选项:
# 1. PostgreSQL (生产环境推荐)
# DATABASE_URI=****************************************************/flask_db

# 2. 本地开发PostgreSQL
# DATABASE_URI=postgresql://flask_user:flask_password@localhost:5432/flask_db_dev

# 3. SQLite (仅开发环境)
# DATABASE_URI=sqlite:///data.db

# 4. MySQL (备选方案)
# DATABASE_URI=mysql://flask_user:flask_password@mysql:3306/flask_db

# 文件路径配置
UPLOAD_FOLDER=uploads
ETL_LOG_FILE=logs/etl.log

# 会话配置
PERMANENT_SESSION_LIFETIME=86400 