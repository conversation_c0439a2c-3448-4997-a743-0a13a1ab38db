import logging
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)

def run_migration(engine):
    """
    Add delta_amount column to payment_schedules if not exists and optional index.
    """
    conn = engine.connect()
    trans = conn.begin()
    try:
        logger.info("检查并添加 payment_schedules.delta_amount 列（如不存在）")
        conn.execute(text(
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'payment_schedules' AND column_name = 'delta_amount'
                ) THEN
                    ALTER TABLE payment_schedules ADD COLUMN delta_amount DOUBLE PRECISION NOT NULL DEFAULT 0;
                END IF;
            END$$;
            """
        ))

        # 可选索引：仅在不存在时创建
        logger.info("创建 delta 索引（如不存在）")
        conn.execute(text(
            """
            CREATE INDEX IF NOT EXISTS ix_payment_schedules_delta
            ON payment_schedules (delta_amount);
            """
        ))

        trans.commit()
        logger.info("payment_schedules.delta_amount 列迁移完成")
        return True
    except SQLAlchemyError as e:
        logger.error(f"添加 delta_amount 列失败: {e}")
        trans.rollback()
        return False
    finally:
        conn.close()

