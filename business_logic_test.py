#!/usr/bin/env python3
"""
Business Logic Validation Test

This script validates that the optimized ETL functions produce identical
business results compared to the original implementation.
"""

import logging
import os
import sys
import json
from datetime import datetime
from decimal import Decimal
from sqlalchemy import create_engine, func
from sqlalchemy.orm import sessionmaker

# Add project root to path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

from etl import get_session, DB_URI
from app.routes.db.models import Order, Transaction, PaymentSchedule

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BusinessLogicValidator:
    """Validates business logic consistency between original and optimized implementations"""
    
    def __init__(self):
        self.engine = create_engine(DB_URI)
        self.Session = sessionmaker(bind=self.engine)
        
    def get_session(self):
        """Get database session"""
        return self.Session()
    
    def validate_financial_calculations(self):
        """Validate financial field calculations are correct"""
        logger.info("Validating financial calculations...")
        
        session = self.get_session()
        validation_results = []
        
        try:
            # Get all orders with their transactions
            orders = session.query(Order).all()
            
            for order in orders:
                # Manual calculation of repaid amount
                repaid_transactions = session.query(Transaction).filter(
                    Transaction.order_id == order.id,
                    Transaction.transaction_type.in_(['首付款', '租金', '尾款'])
                ).all()
                
                manual_repaid = sum(t.amount or 0 for t in repaid_transactions)
                
                # Manual calculation of cost
                cost_transactions = session.query(Transaction).filter(
                    Transaction.order_id == order.id,
                    Transaction.transaction_type.in_(['放款', '供应商利润'])
                ).all()
                
                manual_cost = sum(abs(t.amount or 0) for t in cost_transactions)
                
                # Manual calculation of current receivable
                total_receivable = order.total_receivable or 0
                manual_current_receivable = total_receivable - manual_repaid
                
                # Manual calculation of overdue principal
                manual_overdue_principal = max(manual_cost - manual_repaid, 0)
                
                # Compare with database values
                validation_result = {
                    'order_id': order.id,
                    'order_number': order.order_number,
                    'repaid_amount': {
                        'database': float(order.repaid_amount or 0),
                        'manual': float(manual_repaid),
                        'match': abs((order.repaid_amount or 0) - manual_repaid) < 0.01
                    },
                    'cost': {
                        'database': float(order.cost or 0),
                        'manual': float(manual_cost),
                        'match': abs((order.cost or 0) - manual_cost) < 0.01
                    },
                    'current_receivable': {
                        'database': float(order.current_receivable or 0),
                        'manual': float(manual_current_receivable),
                        'match': abs((order.current_receivable or 0) - manual_current_receivable) < 0.01
                    },
                    'overdue_principal': {
                        'database': float(order.overdue_principal or 0),
                        'manual': float(manual_overdue_principal),
                        'match': abs((order.overdue_principal or 0) - manual_overdue_principal) < 0.01
                    }
                }
                
                validation_results.append(validation_result)
                
                # Log mismatches
                for field in ['repaid_amount', 'cost', 'current_receivable', 'overdue_principal']:
                    if not validation_result[field]['match']:
                        logger.warning(
                            f"Order {order.order_number} {field} mismatch: "
                            f"DB={validation_result[field]['database']}, "
                            f"Manual={validation_result[field]['manual']}"
                        )
        
        finally:
            session.close()
        
        return validation_results
    
    def validate_payment_status_logic(self):
        """Validate payment status calculation logic"""
        logger.info("Validating payment status logic...")
        
        session = self.get_session()
        validation_results = []
        
        try:
            # Get all payment schedules
            payments = session.query(PaymentSchedule).all()
            
            for payment in payments:
                if not payment.order_id:
                    continue
                    
                # Get related transactions for this payment
                order = session.query(Order).filter(Order.id == payment.order_id).first()
                if not order:
                    continue
                
                # Manual status calculation logic
                today = datetime.now().date()
                due_date = payment.due_date
                amount = payment.amount or 0
                paid_amount = payment.paid_amount or 0
                
                # Calculate expected status based on business rules
                if paid_amount >= amount:
                    if due_date and paid_amount > 0:
                        # Check if paid early, on time, or late
                        # This would require transaction dates to determine exactly
                        expected_status = "按时还款"  # Simplified for validation
                    else:
                        expected_status = "按时还款"
                elif due_date and today > due_date:
                    expected_status = "逾期未还"
                else:
                    expected_status = "未到期"
                
                validation_result = {
                    'payment_id': payment.id,
                    'order_number': order.order_number,
                    'period_number': payment.period_number,
                    'status': {
                        'database': payment.status,
                        'expected': expected_status,
                        'match': payment.status == expected_status
                    },
                    'amounts': {
                        'due': float(amount),
                        'paid': float(paid_amount),
                        'remaining': float(amount - paid_amount)
                    }
                }
                
                validation_results.append(validation_result)
                
                # Log status mismatches (but be lenient as status logic is complex)
                if not validation_result['status']['match']:
                    logger.info(
                        f"Payment status difference for Order {order.order_number} "
                        f"Period {payment.period_number}: "
                        f"DB='{payment.status}', Expected='{expected_status}'"
                    )
        
        finally:
            session.close()
        
        return validation_results
    
    def validate_order_status_logic(self):
        """Validate order status calculation logic"""
        logger.info("Validating order status logic...")
        
        session = self.get_session()
        validation_results = []
        
        try:
            orders = session.query(Order).all()
            
            for order in orders:
                # Get payment schedules for this order
                payment_schedules = session.query(PaymentSchedule).filter(
                    PaymentSchedule.order_id == order.id
                ).all()
                
                if not payment_schedules:
                    continue
                
                # Manual order status calculation
                has_overdue = any(ps.status and "逾期未还" in ps.status for ps in payment_schedules)
                all_completed = all(
                    ps.status in ["提前还款", "按时还款", "逾期还款", "协商结清"] 
                    for ps in payment_schedules if ps.status
                )
                
                if has_overdue:
                    expected_status = "逾期"
                elif all_completed and payment_schedules:
                    expected_status = "完结"
                else:
                    expected_status = "在途"
                
                validation_result = {
                    'order_id': order.id,
                    'order_number': order.order_number,
                    'status': {
                        'database': order.status,
                        'expected': expected_status,
                        'match': order.status == expected_status
                    },
                    'payment_schedule_count': len(payment_schedules),
                    'overdue_count': sum(1 for ps in payment_schedules if ps.status and "逾期未还" in ps.status),
                    'completed_count': sum(1 for ps in payment_schedules if ps.status in ["提前还款", "按时还款", "逾期还款", "协商结清"])
                }
                
                validation_results.append(validation_result)
                
                # Log status mismatches
                if not validation_result['status']['match']:
                    logger.warning(
                        f"Order {order.order_number} status mismatch: "
                        f"DB='{order.status}', Expected='{expected_status}'"
                    )
        
        finally:
            session.close()
        
        return validation_results
    
    def generate_validation_report(self, financial_results, payment_results, order_results):
        """Generate comprehensive validation report"""
        
        # Calculate summary statistics
        financial_matches = sum(1 for r in financial_results if all(
            r[field]['match'] for field in ['repaid_amount', 'cost', 'current_receivable', 'overdue_principal']
        ))
        
        payment_matches = sum(1 for r in payment_results if r['status']['match'])
        order_matches = sum(1 for r in order_results if r['status']['match'])
        
        report = {
            'validation_summary': {
                'timestamp': datetime.now().isoformat(),
                'database_uri': DB_URI,
                'financial_validation': {
                    'total_orders': len(financial_results),
                    'perfect_matches': financial_matches,
                    'match_rate': financial_matches / len(financial_results) if financial_results else 0
                },
                'payment_status_validation': {
                    'total_payments': len(payment_results),
                    'status_matches': payment_matches,
                    'match_rate': payment_matches / len(payment_results) if payment_results else 0
                },
                'order_status_validation': {
                    'total_orders': len(order_results),
                    'status_matches': order_matches,
                    'match_rate': order_matches / len(order_results) if order_results else 0
                }
            },
            'detailed_results': {
                'financial_validation': financial_results,
                'payment_status_validation': payment_results,
                'order_status_validation': order_results
            }
        }
        
        # Save report
        report_file = f"business_logic_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Validation report saved to {report_file}")
        return report
    
    def run_full_validation(self):
        """Run complete business logic validation"""
        logger.info("Starting comprehensive business logic validation...")
        
        try:
            # Run all validations
            financial_results = self.validate_financial_calculations()
            payment_results = self.validate_payment_status_logic()
            order_results = self.validate_order_status_logic()
            
            # Generate report
            report = self.generate_validation_report(financial_results, payment_results, order_results)
            
            logger.info("Business logic validation completed successfully")
            return report
            
        except Exception as e:
            logger.error(f"Business logic validation failed: {e}")
            raise

def main():
    """Main function to run business logic validation"""
    logger.info("Business Logic Validation Starting...")
    
    validator = BusinessLogicValidator()
    report = validator.run_full_validation()
    
    # Print summary
    print("\n" + "="*80)
    print("BUSINESS LOGIC VALIDATION SUMMARY")
    print("="*80)
    
    summary = report['validation_summary']
    
    print(f"Financial Calculations: {summary['financial_validation']['perfect_matches']}/{summary['financial_validation']['total_orders']} orders match perfectly ({summary['financial_validation']['match_rate']:.2%})")
    print(f"Payment Status Logic: {summary['payment_status_validation']['status_matches']}/{summary['payment_status_validation']['total_payments']} payments match ({summary['payment_status_validation']['match_rate']:.2%})")
    print(f"Order Status Logic: {summary['order_status_validation']['status_matches']}/{summary['order_status_validation']['total_orders']} orders match ({summary['order_status_validation']['match_rate']:.2%})")
    
    print("="*80)

if __name__ == "__main__":
    main()
