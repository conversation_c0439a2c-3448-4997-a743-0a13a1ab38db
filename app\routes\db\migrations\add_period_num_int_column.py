import logging
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

logger = logging.getLogger(__name__)


def run_migration(engine):
    """
    Ensure transactions.period_num_int exists and add helpful index.
    - Adds INTEGER column `period_num_int` if missing
    - Creates index (order_id, period_num_int, transaction_type) if missing
    """
    conn = engine.connect()
    trans = conn.begin()
    try:
        logger.info("检查并添加 transactions.period_num_int 列（如不存在）")
        conn.execute(text(
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_schema = 'public' AND table_name = 'transactions' AND column_name = 'period_num_int'
                ) THEN
                    ALTER TABLE transactions ADD COLUMN period_num_int INTEGER;
                END IF;
            END$$;
            """
        ))

        logger.info("创建 transactions 期次相关索引（如不存在）")
        conn.execute(text(
            """
            CREATE INDEX IF NOT EXISTS ix_transactions_order_period_type
            ON transactions (order_id, period_num_int, transaction_type);
            """
        ))

        trans.commit()
        logger.info("transactions.period_num_int 列迁移完成")
        return True
    except SQLAlchemyError as e:
        logger.error(f"添加 period_num_int 列失败: {e}")
        trans.rollback()
        return False
    finally:
        conn.close()

