# app/__init__.py

from flask import Flask, jsonify
from app.config import DevelopmentConfig, ProductionConfig
from app.utils.logging_config import setup_logging
from app.routes import register_blueprints
import logging
import secrets
import os
from scheduler import init_scheduler  # 导入定时任务初始化函数


def create_app():
    app = Flask(__name__, instance_relative_config=True)

    # 确保实例文件夹存在（用于持久化 SECRET_KEY）
    try:
        os.makedirs(app.instance_path, exist_ok=True)
    except OSError:
        pass

    # 从环境变量或实例文件加载/生成持久化的 SECRET_KEY
    secret_key = os.environ.get('SECRET_KEY')
    if not secret_key:
        key_path = os.path.join(app.instance_path, 'secret_key')
        try:
            if os.path.exists(key_path):
                with open(key_path, 'r') as f:
                    secret_key = f.read().strip()
            else:
                secret_key = secrets.token_hex(32)
                with open(key_path, 'w') as f:
                    f.write(secret_key)
        except Exception as e:
            logging.warning(f'加载/写入SECRET_KEY失败，将使用临时密钥: {e}')
            secret_key = secrets.token_hex(32)

    # 设置默认配置
    app.config.from_mapping(
        SECRET_KEY=secret_key,
        SESSION_TYPE='filesystem',
        PERMANENT_SESSION_LIFETIME=86400,  # 会话有效期1天
        DATABASE_URI=os.environ.get(
            'DATABASE_URI',
            'postgresql://flask_user:flask_password@localhost:5433/flask_db'
        ),
        UPLOAD_FOLDER=os.environ.get('UPLOAD_FOLDER', 'uploads'),
        ETL_LOG_FILE=os.environ.get('ETL_LOG_FILE', 'etl.log'),
        # 禁用模板缓存以确保开发时模板更新能立即生效
        TEMPLATES_AUTO_RELOAD=True,
        SEND_FILE_MAX_AGE_DEFAULT=0
    )

    # 选择配置类
    env = app.config.get('ENV')
    if env == 'production':
        app.config.from_object(ProductionConfig)
    else:
        app.config.from_object(DevelopmentConfig)

    # 确保上传文件夹存在
    try:
        os.makedirs(
            os.path.join(app.root_path, app.config['UPLOAD_FOLDER']),
            exist_ok=True
        )
    except OSError:
        pass

    # 设置日志
    setup_logging()

    # 注册蓝图
    register_blueprints(app)
    from app.routes import auth
    app.register_blueprint(auth.bp)
    # 初始化定时任务调度器
    init_scheduler(app)

    # 健康检查端点
    @app.route('/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'message': 'Flask API is running',
            'version': '1.0.0'
        }), 200

    # 设置默认路由为登录页面
    @app.route('/')
    def index():
        return auth.login_page()

    # 全局错误处理
    @app.errorhandler(500)
    def internal_error(error):
        logging.error(f'服务器内部错误: {error}')
        return jsonify({'error': '服务器内部错误，请联系管理员。'}), 500

    @app.errorhandler(404)
    def not_found_error(error):
        return jsonify({'error': '资源未找到。'}), 404

    return app
