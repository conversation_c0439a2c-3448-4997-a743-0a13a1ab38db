# ETL 导入与账单计算规则（项目交接文档）

更新时间：2025-09-12

## 目标
- 明确 Excel → 数据库 的导入流程、字段映射与校验规则
- 固化不同产品类型（租赁、电商）的逐期应还金额计算方式
- 说明交易归属规则（尤其首付款）与账单状态/财务字段更新逻辑
- 给出性能、索引、迁移与运维要点

## 数据源与表
- Excel 工作簿
  - 订单管理（核心订单信息与账单期数/日期/每期还款金）
  - 资金流水账（交易流水，含首付款/租金/尾款等）
  - @芳会资料补充（客户补充信息）
- 数据库表
  - `orders`、`payment_schedules`、`transactions`、`customer_info`
  - 关键新增列：
    - `transactions.period_num_int`（标准化期次，便于聚合与匹配）
    - `payment_schedules.auto_inferred`（系统推断生成的账单标记）

## 导入流程（高层）
1. 预检查与迁移（自动/脚本）
   - 确保列存在：`transactions.period_num_int`、`payment_schedules.auto_inferred`
   - 索引按迁移脚本集中创建，不在 ETL 时重复创建
2. 读取 Excel（三张表一次性加载并清洗列名/数字格式）
3. 构建映射
   - `dp_map`：按订单编号聚合“资金流水账”中交易类型为“首付款”的金额
   - `order_id_map`：按订单编号一次性查出订单 ID（避免 N+1）
4. 清空核心表（PostgreSQL 使用 `TRUNCATE ... RESTART IDENTITY CASCADE`）
5. 导入订单（`orders`）
   - 期数 `periods`：优先“期数”字段；缺失则尝试从“产品类型”提取（如“6期”）
   - 台数 `devices_count`：默认 1，若存在列则取整
   - 总待收、当前待收、成本等字段直接映射
6. 生成还款计划（`payment_schedules`）
   - 期次日期来源：优先 Excel 中“第X期”列的日期；缺失则以订单日期按月顺延补齐（`auto_inferred=True`）
   - 金额来源（优先级）：
     1) Excel“每期还款金”列若有值，则 1..periods 每期金额=该值
     2) 针对“电商”产品：按公式反推
        - 每期金额 = (总待收 − 首付款总额) / 期数
        - 首付款总额优先使用 Excel“首付/首付款”单价×台数；否则 `pricing.csv` 默认首付×台数；否则回退 `dp_map`
     3) 针对“租赁”产品：按新规则
        - 每期金额 = (总待收 − 买断金×台数) / 期数（不在账单中扣首付）
     4) 其余或缺数据：回退“每期还款金”（若有）或记警告
   - 4+2 买断金拆分（当产品为“租”且 `periods=4`）：
     - 第5期=买断金/2，第6期=买断金/2（不扣首付）
     - 若 Excel 未提供第5/6期日期，系统按第4期或订单日期顺延月数补齐，`auto_inferred=True`
7. 导入交易（`transactions`）
   - `period_num_int`：从“归属期数”解析（支持“1/第1期/M1/买一”等）
   - 数值/字符串安全转换与长度截断
8. 导入客户（`customer_info`）
9. 提交并产出导入报告与日志

## 账单状态与财务字段更新
- 交易归属到期次（核心规则）
  - 首付款交易：归属到该订单的“最后一期”（6期或第6/4+2 的第6期）
  - 租金/尾款：按 `period_num_int` 精确匹配
- `payment_schedules` 更新
  - `paid_amount`：按归属聚合后的实收金额
  - `delta_amount`：应还−实还
  - `status`：根据到期日/实收情况更新（逾期、提前、按时、部分还款等）
- `orders` 财务字段
  - `repaid_amount`：订单所有（首付款/租金/尾款）金额之和
  - `overdue_principal`：成本−已还，最小 0
  - `current_receivable`：总待收−已还，最小 0

## 定价与型号归一
- `pricing.csv`：按 `product_type + model + periods` 命中
  - `buyout_total`、`downpayment_default`、`rent_per_period` 等按台数放大
- 型号归一：标准化空格/分隔符/GB 标记，保留常见罗马数字替换（不再全局替换 `x→10`，避免将 “promax” 误写为 “proma10”）

## 日志与导入报告
- 日志：`logs/etl.log`（详述每阶段统计、规则分支与警告）
- 报告：`/api/etl/report` 下载/查看
  - summary：订单/账单统计、合同校验通过/失败、定价命中与反推次数
  - issues：
    - `contract_mismatch`：合同（总待收）与计算不一致
    - `period_conflict`：文本期次提示与金额冲突（阈值 100）
    - `missing_p56`：自动补齐了第5/第6期
    - `partial_payment`：部分还款
    - `pricing_miss`：定价未命中（按需要常规化型号后重试）

## 性能要点（建议）
- 映射/批量化
  - 交易订单映射一次性查全量（避免 N+1）
  - PaymentSchedule/Transaction 使用 `bulk_save_objects` 分批插入（1000~2000/批）
- 复用已读 DataFrame，避免重复 `read_excel`
- 日志降噪：逐条改 DEBUG，批次保留 INFO
- SQL 聚合替代 Python 汇总
  - 订单与期次的已还金额用 `GROUP BY` 汇总后批量 `UPDATE`
- 索引
  - 已有：`transactions (transaction_date, order_id, transaction_type)`，`(order_id, period_num_int, transaction_type)`
  - 建议新增：`payment_schedules (order_id, period_number)`

## 迁移与运行
- 迁移脚本：`python3 run_migrations.py`
  - 新增列：`payment_schedules.auto_inferred`、`transactions.period_num_int`
  - 性能索引：见 `app/routes/db/migrations/add_performance_indexes.py`
- 开发运行：
  - `python start_dev.py` 或 `python run.py`
  - 触发导入：`POST /api/etl/upload`（支持文件上传）或 `POST /api/etl/trigger`（指定路径）

## 异常与边界
- 缺“总待收”/“期数”时的回退：优先 Excel“每期还款金”，否则记警告
- 多台设备：买断金/首付/定价按台数放大；总待收通常为整单金额
- 自动补期：仅在必要时补齐（标记 `auto_inferred`），并清晰记录日志

## 代码入口与主要位置
- ETL 主流程与规则：`etl.py`
- ETL API：`app/routes/etl_api.py`
- ORM 模型：`app/routes/db/models.py`
- 迁移：`app/routes/db/migrations/*.py`，入口 `run_migrations.py`

---
如需深入性能/SQL 层面的配置与基准，可参考 `TECHNICAL_SOLUTIONS.md` 并结合生产库 `EXPLAIN ANALYZE` 输出做针对性调整。

