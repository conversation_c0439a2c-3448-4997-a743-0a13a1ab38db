# scheduler.py
# 定时任务模块，负责设置和管理定时任务

import sys
import os
import logging
from flask_apscheduler import APScheduler
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# 导入ETL模块中的函数
from etl import update_payment_status_and_receivable, update_order_status, update_financial_fields, create_performance_indexes, Base, DB_URI

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# 添加文件处理器，记录到独立的日志文件
scheduler_log_file = os.getenv('SCHEDULER_LOG_FILE', 'logs/scheduler.log')

# 确保日志目录存在
log_dir = os.path.dirname(scheduler_log_file)
if log_dir and not os.path.exists(log_dir):
    os.makedirs(log_dir, exist_ok=True)

fh = logging.FileHandler(scheduler_log_file, encoding='utf-8')
fh.setLevel(logging.INFO)
fh.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
logger.addHandler(fh)

# 初始化调度器
scheduler = APScheduler()

def get_db_session():
    """获取数据库会话"""
    try:
        engine = create_engine(DB_URI, echo=False)
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        return Session()
    except Exception as e:
        logger.error(f"创建数据库会话失败: {str(e)}")
        return None

def update_payment_status_job():
    """更新还款状态的定时任务"""
    try:
        logger.info("开始执行还款状态自动更新任务")
        start_time = datetime.datetime.now()
        
        # 获取数据库会话
        session = get_db_session()
        if not session:
            logger.error("无法获取数据库会话，任务终止")
            return
        
        try:
            # 调用现有的更新函数，不改变业务逻辑
            update_payment_status_and_receivable(session)
            
            # 更新订单状态
            update_order_status(session)
            
            # 更新财务字段（已还金额、逾期本金、当前待收）
            update_financial_fields(session)
            
            # 创建和更新数据库索引，优化查询性能
            engine = create_engine(DB_URI, echo=False)
            create_performance_indexes(engine)
            logger.info("数据库索引创建和更新完成")
            
            # 计算执行时间
            end_time = datetime.datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"数据更新任务完成，耗时 {duration:.2f} 秒")
        except Exception as e:
            logger.error(f"数据更新失败: {str(e)}", exc_info=True)
        finally:
            session.close()
    except Exception as e:
        logger.error(f"执行数据更新任务时发生错误: {str(e)}", exc_info=True)

def init_scheduler(app=None):
    """初始化调度器并添加任务"""
    if app:
        scheduler.init_app(app)
        
        # 配置任务
        scheduler.add_job(
            id='update_payment_status', 
            func=update_payment_status_job, 
            trigger='cron', 
            hour=1,  # 每天凌晨1点
            minute=0,  # 整点
            second=0,  # 0秒
            timezone='Asia/Shanghai'  # 设置为中国时区
        )
        
        logger.info("已设置还款状态更新任务: 每日凌晨1:00自动执行")
        
        # 启动调度器
        scheduler.start()
        return scheduler
    return None

# 如果直接运行此文件，执行一次更新任务（用于测试）
if __name__ == "__main__":
    update_payment_status_job()
