# etl.py
# Phase 1: ETL 脚本，将 Excel 数据同步到本地 SQLite 数据库，且不改动现有 Flask 业务逻辑

import os
import pandas as pd
from dotenv import load_dotenv
from sqlalchemy import create_engine, Column, Integer, String, Date, Float, Text, ForeignKey, func, or_, text, Bo<PERSON>an
from sqlalchemy.orm import declarative_base, relationship, sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import sqlalchemy.exc
import logging
import datetime
from dateutil.relativedelta import relativedelta
import re
import time
import csv

# 加载环境变量
load_dotenv()
DB_URI = os.getenv('DATABASE_URI', 'postgresql://flask_user:flask_password@localhost:5433/flask_db')  # 默认 PostgreSQL 数据库
EXCEL_PATH = os.getenv('EXCEL_FILE_PATH', 'TTXW.xlsm')  # 默认 Excel 文件

# 定义 ORM 基类
Base = declarative_base()

# ----------------------
# Pricing configuration
# ----------------------

def _normalize_text(s: str) -> str:
    if s is None:
        return ''
    return re.sub(r"\s+", "", str(s)).lower()


def _normalize_model(s: str) -> str:
    """Normalize model names to improve matching.

    - lowercase, remove spaces
    - unify 'pro max' -> 'promax'
    - remove separators -_/.
    - unify capacity suffix: 'gb' -> 'g'
    - convert roman numerals common for iphone (xvi -> 16) if present (best-effort)
    """
    if s is None:
        return ''
    x = str(s).strip().lower()
    # remove separators and spaces
    x = re.sub(r"[\s\-_/\\.]+", "", x)
    # unify pro max
    x = x.replace('promax', 'promax').replace('pro max', 'promax')
    # capacity normalization
    x = x.replace('gb', 'g')
    # common roman numerals replacement (avoid replacing generic 'x' to not break 'promax')
    x = x.replace('xvi', '16').replace('xv', '15').replace('xiv', '14').replace('xiii', '13').replace('xii', '12').replace('xi', '11')
    return x


def load_pricing_config():
    """Load pricing config from config/pricing.csv if present.

    Returns a list of dict rows. Missing file returns empty list.
    """
    candidates = [
        os.path.join(os.getcwd(), 'config', 'pricing.csv'),
        os.path.join(os.path.dirname(__file__), 'config', 'pricing.csv'),
        os.path.join(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')), 'config', 'pricing.csv')
    ]
    path = None
    for p in candidates:
        if os.path.exists(p):
            path = p
            break
    if not path:
        return []

    rows = []
    try:
        with open(path, newline='', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # normalize some fields
                row['product_type_norm'] = _normalize_text(row.get('product_type'))
                row['model_norm_norm'] = _normalize_model(row.get('model_norm'))
                row['model_aliases_norm'] = [_normalize_model(x) for x in (row.get('model_aliases') or '').split(';') if x]
                # numeric conversions (safe)
                def to_float(val, default=None):
                    try:
                        return float(val)
                    except Exception:
                        return default
                def to_int(val, default=None):
                    try:
                        return int(val)
                    except Exception:
                        return default
                row['periods_int'] = to_int(row.get('periods'))
                row['billing_months_int'] = to_int(row.get('billing_months'))
                row['rent_per_period_float'] = to_float(row.get('rent_per_period'))
                row['buyout_total_float'] = to_float(row.get('buyout_total'), 0.0)
                row['downpayment_default_float'] = to_float(row.get('downpayment_default'), 0.0)
                rows.append(row)
        if 'logger' in globals():
            logger.info(f"载入pricing配置: {len(rows)} 条 from {path}")
        else:
            print(f"载入pricing配置: {len(rows)} 条 from {path}")
    except Exception as e:
        if 'logger' in globals():
            logger.warning(f"加载pricing配置失败: {e}")
        else:
            print(f"加载pricing配置失败: {e}")
    return rows


PRICING_CONFIG = load_pricing_config()


def find_pricing(product_type: str, model: str, periods: int):
    """Find pricing row by product_type, normalized model, and periods.

    Matching priority: exact model_norm -> alias match -> wildcard (* not implemented in CSV comments).
    Returns dict or None.
    """
    if not PRICING_CONFIG:
        return None
    pt = _normalize_text(product_type)
    mn = _normalize_model(model)
    # exact match
    for row in PRICING_CONFIG:
        if row['product_type_norm'] == pt and row['periods_int'] == periods and row['model_norm_norm'] == mn:
            return row
    # alias match
    for row in PRICING_CONFIG:
        if row['product_type_norm'] == pt and row['periods_int'] == periods and mn in row['model_aliases_norm']:
            return row
    # fallback: first row with same pt+periods (acts like a default)
    for row in PRICING_CONFIG:
        if row['product_type_norm'] == pt and row['periods_int'] == periods:
            return row
    return None

class Order(Base):
    __tablename__ = 'orders'
    id = Column(Integer, primary_key=True)
    order_date = Column(Date)
    order_number = Column(String, unique=True, index=True)
    customer_name = Column(String)
    model = Column(String)
    customer_attribute = Column(String)
    usage = Column(String)
    payment_cycle = Column(String)
    product_type = Column(String)
    periods = Column(Integer)
    business_type = Column(String)
    total_receivable = Column(Float)
    current_receivable = Column(Float)
    remarks = Column(Text)
    cost = Column(Float)
    shop_affiliation = Column(String)
    devices_count = Column(Integer, default=1)  # 台数字段，默认为1
    status = Column(String, default='在途')  # 状态字段，默认为在途
    repaid_amount = Column(Float, default=0.0)  # 已还金额
    overdue_principal = Column(Float, default=0.0)  # 逾期本金
    payment_schedules = relationship('PaymentSchedule', back_populates='order', cascade='all, delete-orphan')
    transactions = relationship('Transaction', back_populates='order', cascade='all, delete-orphan')
    customer_info = relationship('CustomerInfo', back_populates='order', uselist=False, cascade='all, delete-orphan')

class PaymentSchedule(Base):
    __tablename__ = 'payment_schedules'
    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id'))
    period_number = Column(Integer)
    due_date = Column(Date)
    amount = Column(Float)
    paid_amount = Column(Float, default=0)  # 已还金额
    delta_amount = Column(Float, default=0)  # 当期差额（应还-实还）
    status = Column(String)
    auto_inferred = Column(Boolean, default=False)  # 是否为系统自动补期
    order = relationship('Order', back_populates='payment_schedules')

class Transaction(Base):
    __tablename__ = 'transactions'
    id = Column(Integer, primary_key=True)
    transaction_date = Column(Date)
    order_id = Column(Integer, ForeignKey('orders.id'))
    customer_name = Column(String)
    model = Column(String)
    customer_attribute = Column(String)
    usage = Column(String)
    payment_cycle = Column(String)
    product_type = Column(String)
    amount = Column(Float)
    period_number = Column(String)
    period_num_int = Column(Integer)
    transaction_type = Column(String)
    direction = Column(String)
    transaction_order_number = Column(String)
    available_balance = Column(Float)
    pending_withdrawal = Column(Float)
    remarks = Column(Text)
    order = relationship('Order', back_populates='transactions')

class CustomerInfo(Base):
    __tablename__ = 'customer_info'
    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id'), unique=True)
    order_number = Column(String, unique=True)
    customer_name = Column(String)
    phone = Column(String)
    rental_period = Column(String)
    customer_service = Column(String)
    business_affiliation = Column(String)
    remarks = Column(Text)
    order = relationship('Order', back_populates='customer_info')

logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# 设置 ETL 导入详细日志文件
LOG_FILE = os.getenv('ETL_LOG_FILE', 'logs/etl.log')

# 确保日志目录存在
log_dir = os.path.dirname(LOG_FILE)
if log_dir and not os.path.exists(log_dir):
    os.makedirs(log_dir, exist_ok=True)

fh = logging.FileHandler(LOG_FILE, encoding='utf-8')
fh.setLevel(logging.DEBUG)
fh.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(message)s'))
logger.addHandler(fh)

def ensure_transactions_period_num_int(session_or_engine):
    """Ensure transactions.period_num_int exists using robust IF NOT EXISTS DDL.

    Accepts a Session or Engine.
    """
    try:
        engine = session_or_engine.get_bind() if hasattr(session_or_engine, 'get_bind') else session_or_engine
        with engine.connect() as conn:
            try:
                logger.info('确保 transactions.period_num_int 列存在（DDL IF NOT EXISTS）')
                conn.execute(text("ALTER TABLE IF EXISTS transactions ADD COLUMN IF NOT EXISTS period_num_int INTEGER"))
                conn.commit()
            except Exception as e:
                logger.warning(f'添加 transactions.period_num_int 列失败或已存在: {e}')
            # 验证列是否存在
            try:
                res = conn.execute(text(
                    """
                    SELECT 1 FROM information_schema.columns
                    WHERE table_schema = current_schema()
                      AND table_name = 'transactions'
                      AND column_name = 'period_num_int'
                    """
                )).fetchone()
                if not res:
                    raise RuntimeError('数据库缺少 transactions.period_num_int 列，且无法自动添加，请授予 ALTER TABLE 权限或手动执行迁移。')
            except Exception as e:
                logger.error(f'验证 transactions.period_num_int 列失败: {e}')
                raise
    except Exception as e:
        logger.warning(f'检查/添加 transactions.period_num_int 列异常: {e}')

# (moved earlier) ensure_payment_schedules_auto_inferred

def get_session(db_uri):
    engine = create_engine(db_uri, echo=False)
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    return Session()

def extract_period_number(period_str):
    """从期数字符串中提取数字部分
    
    可以处理的格式:
    - 纯数字: "6" -> 6
    - 带"期"的: "6期" -> 6 
    - 带其他文本的: "分6期" -> 6
    """
    if period_str is None or pd.isnull(period_str):
        return None
        
    # 如果是纯数字，直接转换
    period_str = str(period_str).strip()
    # 特殊别名处理（4+2）：买一/买二/M1/M2
    if '买一' in period_str:
        return 5
    if '买二' in period_str:
        return 6
    if re.search(r'\b[Mm]1\b', period_str):
        return 5
    if re.search(r'\b[Mm]2\b', period_str):
        return 6
    if period_str.isdigit():
        return int(period_str)
    
    # 使用正则表达式提取数字部分
    match = re.search(r'(\d+)', period_str)
    if match:
        return int(match.group(1))
    
    return None

# removed duplicate ensure_payment_schedules_auto_inferred definition (moved earlier)

def sync_to_db(session, excel_path):
    """
    将Excel数据同步到数据库
    
    Args:
        session: 数据库会话
        excel_path: Excel文件路径
        
    Returns:
        tuple: (orders_count, schedules_count, transactions_count, customers_count)
    """
    
    # 初始化计数器
    orders_count = 0
    schedules_count = 0
    transactions_count = 0
    customers_count = 0
    
    # 失败统计
    failed_orders = []
    failed_schedules = []
    failed_transactions = []
    failed_customers = []
    
    # 使用事务处理，并添加重试机制
    retry_count = 0
    max_retries = 3
    retry_interval = 2  # 秒
    
    while retry_count <= max_retries:
        try:
            # 清空指定的四个表，避免数据重复
            logger.info('清空核心数据表，准备重新导入数据')
            
            # 使用更强制的方式清空表，禁用外键约束检查
            engine = session.get_bind()
            with engine.connect() as conn:
                # 关闭自动提交，开始事务
                trans = conn.begin()
                try:
                    if engine.dialect.name == 'postgresql':
                        logger.info('PostgreSQL：使用 TRUNCATE ... RESTART IDENTITY CASCADE 清空核心表')
                        conn.execute(text("TRUNCATE TABLE payment_schedules, transactions, customer_info, orders RESTART IDENTITY CASCADE"))
                        trans.commit()
                        logger.info('核心数据表已 TRUNCATE 并重置序列')
                    else:
                        # 回退方案：逐表 DELETE（适用于 SQLite 等）
                        logger.info('非 PostgreSQL：逐表 DELETE 清空核心表')
                        logger.info('正在清空 PaymentSchedule 表...')
                        conn.execute(text("DELETE FROM payment_schedules"))
                        logger.info('正在清空 Transaction 表...')
                        conn.execute(text("DELETE FROM transactions"))
                        logger.info('正在清空 CustomerInfo 表...')
                        conn.execute(text("DELETE FROM customer_info"))
                        logger.info('正在清空 Order 表...')
                        conn.execute(text("DELETE FROM orders"))
                        trans.commit()
                        logger.info('核心数据表已清空')
                except Exception as e:
                    trans.rollback()
                    logger.error(f'清空表失败，已回滚: {str(e)}')
                    raise
            
            # 读取Excel文件
            try:
                logger.info('读取Excel文件: %s', excel_path)
                df_orders = pd.read_excel(excel_path, sheet_name='订单管理', engine='openpyxl')
                df_transactions = pd.read_excel(excel_path, sheet_name='资金流水账', engine='openpyxl')
                df_customers = pd.read_excel(excel_path, sheet_name='@芳会资料补充', engine='openpyxl')
                logger.info('Excel文件读取成功')
                
                # Excel数据统计
                df_orders.columns = df_orders.columns.str.strip().str.replace('﻿', '')
                df_transactions.columns = df_transactions.columns.str.strip().str.replace('﻿', '')
                df_customers.columns = df_customers.columns.str.strip().str.replace('﻿', '')
                
                # 统计Excel中的有效数据
                valid_orders_excel = len(df_orders[df_orders['订单编号'].notna() & (df_orders['订单编号'] != '')])
                df_transactions['交易金额'] = pd.to_numeric(df_transactions['交易金额'], errors='coerce')
                valid_transactions_excel = len(df_transactions[df_transactions['交易金额'].notnull()])
                valid_customers_excel = len(df_customers[df_customers['订单编号'].notna() & (df_customers['订单编号'] != '')])
                
                # 计算还款计划数
                schedule_cols = [c for c in df_orders.columns if c.startswith('第') and c.endswith('期')]
                total_schedules_excel = 0
                valid_orders_df = df_orders[df_orders['订单编号'].notna() & (df_orders['订单编号'] != '')]
                for idx, row in valid_orders_df.iterrows():
                    for col in schedule_cols:
                        if pd.notnull(row[col]):
                            total_schedules_excel += 1
                
                logger.info('Excel数据统计: 订单=%d, 还款计划=%d, 交易=%d, 客户信息=%d', 
                           valid_orders_excel, total_schedules_excel, valid_transactions_excel, valid_customers_excel)

            except Exception as e:
                logger.error(f"读取Excel文件失败: {str(e)}")
                raise

            # 预构建首付款映射：从资金流水账按订单编号汇总“首付款”
            try:
                df_txn_dp = df_transactions.copy()
                df_txn_dp['交易金额'] = pd.to_numeric(df_txn_dp['交易金额'], errors='coerce')
                df_txn_dp = df_txn_dp[(df_txn_dp['交易类型'].fillna('').astype(str).str.strip() == '首付款') & (df_txn_dp['交易金额'].notnull())]
                dp_map = {}
                for _, rr in df_txn_dp.iterrows():
                    on = rr.get('订单编号')
                    if pd.isnull(on):
                        continue
                    key = str(on).strip()
                    dp_map[key] = dp_map.get(key, 0.0) + float(rr['交易金额'])
                logger.info('已构建首付款映射：%d 个订单含首付', len(dp_map))
            except Exception as e:
                logger.warning(f'构建首付款映射失败: {e}')
                dp_map = {}

            # 在处理还款计划前确保 payment_schedules.auto_inferred 列存在
            try:
                ensure_payment_schedules_auto_inferred(session)
            except Exception as _e:
                logger.warning(f'确保 payment_schedules.auto_inferred 列可用失败: {_e}')

            # 同步订单管理
            logger.info('同步订单管理开始')
            df_orders.columns = df_orders.columns.str.strip().str.replace('﻿', '')
            logger.info('订单管理表有 %d 行数据', len(df_orders))
            
            # 获取还款计划相关列
            schedule_cols = [c for c in df_orders.columns if c.startswith('第') and c.endswith('期')]
            logger.info('识别到还款计划列: %s', schedule_cols)
            
            if not schedule_cols:
                logger.warning('未找到还款计划列，请检查Excel文件格式')
            
            cn_map = {'一':1,'二':2,'三':3,'四':4,'五':5,'六':6,'七':7,'八':8,'九':9,'十':10}
            period_field = next((c for c in df_orders.columns if '期数' in str(c) and not str(c).startswith('第')), None)
            
            # 查找台数字段
            devices_count_field = next((c for c in df_orders.columns if '台数' in str(c)), None)
            logger.info('台数字段识别: %s', devices_count_field)
            
            # 在处理前先去重订单编号
            processed_order_numbers = set()  # 用来跟踪已处理的订单编号
            skipped_duplicates = 0  # 跳过的重复订单数量
            
            # 同步订单数据
            batch_size = 50  # 每批处理的订单数
            for i in range(0, len(df_orders), batch_size):
                batch = df_orders.iloc[i:i+batch_size]
                logger.info(f'处理订单批次 {i//batch_size + 1}/{(len(df_orders)-1)//batch_size + 1}, 行 {i}-{min(i+batch_size-1, len(df_orders)-1)}')
                
                for idx, r in batch.iterrows():
                    order_number_raw = r.get('订单编号')
                    if pd.isnull(order_number_raw) or not str(order_number_raw).strip():
                        logger.warning('第 %d 行订单管理: 订单编号为空，跳过', idx)
                        continue
                        
                    order_number = str(order_number_raw).strip()
                    
                    # 检查是否已处理过这个订单编号
                    if order_number in processed_order_numbers:
                        logger.warning('第 %d 行订单管理: 订单编号 %s 重复，跳过', idx, order_number)
                        skipped_duplicates += 1
                        continue
                    
                    # 标记为已处理
                    processed_order_numbers.add(order_number)
                    order = Order()  # 总是创建新订单，因为我们已经清空了表
                    
                    date_raw = r.get('日期')
                    ts_date = pd.to_datetime(date_raw, errors='coerce')
                    if pd.isnull(ts_date):
                        logger.warning('订单 %s: 无效日期 %s，设为 None', order_number, date_raw)
                        order.order_date = None
                    else:
                        order.order_date = ts_date.date()
                        
                    # 设置订单基本信息    
                    order.order_number = order_number
                    order.customer_name = r.get('客户姓名')
                    order.model = r.get('型号')
                    order.customer_attribute = r.get('客户属性')
                    order.usage = r.get('用途')
                    order.payment_cycle = r.get('还款周期')
                    order.product_type = r.get('产品')
                    
                    # 读取台数字段
                    if devices_count_field and not pd.isnull(r.get(devices_count_field)):
                        devices_count_val = r.get(devices_count_field)
                        try:
                            # 尝试将台数转换为整数
                            devices_count = int(float(devices_count_val))
                            if devices_count <= 0:  # 确保值有效
                                logger.warning('订单 %s: 台数值无效 %s，设置为默认值1', order_number, devices_count_val)
                                order.devices_count = 1
                            else:
                                order.devices_count = devices_count
                                logger.info('订单 %s: 设置台数为 %d', order_number, devices_count)
                        except (ValueError, TypeError):
                            logger.warning('订单 %s: 台数值转换失败 %s，设置为默认值1', order_number, devices_count_val)
                            order.devices_count = 1
                    else:
                        # 默认为1台
                        order.devices_count = 1
                    
                    # 修改期数处理逻辑
                    period_val = r.get(period_field) if period_field else None
                    # 尝试从产品类型中获取期数信息（如果产品类型包含期数信息）
                    if period_field is None or pd.isnull(period_val) or period_val == '' or period_val == 0:
                        # 尝试从产品类型中提取期数（如"6期"）
                        if not pd.isnull(order.product_type):
                            period_from_product = extract_period_number(order.product_type)
                            if period_from_product:
                                logger.info('订单 %s: 从产品类型 "%s" 中提取期数: %d', 
                                          order_number, order.product_type, period_from_product)
                                order.periods = period_from_product
                            else:
                                order.periods = None
                        else:
                            order.periods = None
                    else:
                        # 直接从期数字段中提取数字
                        order.periods = extract_period_number(period_val)
                        logger.info('订单 %s: 期数字段值 "%s", 提取期数: %s', 
                                   order_number, period_val, order.periods)
                    
                    order.business_type = r.get('业务')
                    order.total_receivable = float(r.get('总待收')) if pd.notnull(r.get('总待收')) else None
                    order.current_receivable = float(r.get('当前待收')) if pd.notnull(r.get('当前待收')) else None
                    order.remarks = r.get('备注')
                    order.cost = float(r.get('成本')) if pd.notnull(r.get('成本')) else None
                    order.shop_affiliation = r.get('店铺归属')
                    
                    # 添加订单到数据库
                    try:
                        session.add(order)
                        session.flush()  # 立即刷新以获取ID
                        
                        # 刷新实例以获取数据库生成的主键 id
                        session.refresh(order)
                        orders_count += 1
                        logger.info('订单 %s 已同步，ID=%d', order_number, order.id)
                    except Exception as e:
                        failed_orders.append({
                            'row': idx,
                            'order_number': order_number,
                            'error': str(e),
                            'data': dict(r)
                        })
                        logger.error('订单 %s (第 %d 行) 导入失败: %s', order_number, idx, str(e))
                        session.rollback()
                        continue
                    
                    # 同步还款计划
                    payment_plans_added = 0
                    
                    # 获取每期还款金额
                    payment_amount = None
                    if not pd.isnull(r.get('每期还款金')):
                        try:
                            payment_amount = float(r.get('每期还款金'))
                            logger.info('订单 %s: 每期还款金额 = %s', order_number, payment_amount)
                        except (ValueError, TypeError):
                            logger.warning('订单 %s: 每期还款金额转换失败 %s', 
                                         order_number, r.get('每期还款金'))
                    
                    # 生成逐期应还金额（新规则：按总待收-首付-买断金 再除期数）
                    expected_amounts = {}
                    try:
                        product_type_val = (order.product_type or '')
                        is_lease = '租' in str(product_type_val)
                        is_ecommerce = '电商' in str(product_type_val)
                        devices = order.devices_count or 1
                        periods = order.periods or 0
                        total_receivable = order.total_receivable if order.total_receivable is not None else None

                        # 获取买断金（来自 pricing.csv）
                        buyout_total = None
                        pr = find_pricing(product_type_val, order.model or '', periods)
                        if pr and pr.get('buyout_total_float') is not None:
                            buyout_total = float(pr['buyout_total_float']) * devices
                        else:
                            buyout_total = 0.0

                        # 优先使用 Excel 的“每期还款金”
                        if payment_amount is not None and periods > 0:
                            for p in range(1, periods + 1):
                                expected_amounts[p] = float(payment_amount)
                            logger.info(f"订单 {order_number}: 使用Excel每期还款金优先填充，periods={periods}, amount={payment_amount}")
                        # 电商产品反推：每期=(总待收-首付总额)/期数（首付按台数）
                        elif is_ecommerce and periods > 0 and total_receivable is not None:
                            def _safe_num(v):
                                try:
                                    return float(v)
                                except Exception:
                                    return None
                            # 优先使用 Excel 的首付单价，其次 pricing 的默认首付单价；否则退回交易中的首付总额
                            dp_unit = _safe_num(r.get('首付')) or _safe_num(r.get('首付款'))
                            if dp_unit is not None:
                                dp_total = dp_unit * devices
                            else:
                                pr2 = find_pricing(product_type_val, order.model or '', periods)
                                d = pr2.get('downpayment_default_float') if pr2 else None
                                if d is not None:
                                    dp_total = float(d) * devices
                                else:
                                    dp_total = float(dp_map.get(order_number, 0.0))
                            rent_total = float(total_receivable) - dp_total
                            if rent_total < 0:
                                logger.warning(f"订单 {order_number}: 电商反推得到租金总额为负({rent_total})，置0并记账；total={total_receivable}, dp_total={dp_total}, periods={periods}")
                                rent_total = 0.0
                            base = round(rent_total / periods, 2) if periods > 0 else 0.0
                            remainder = round(rent_total - base * periods, 2)
                            for p in range(1, periods + 1):
                                expected_amounts[p] = base
                            if remainder != 0 and periods >= 1:
                                expected_amounts[periods] = round(expected_amounts[periods] + remainder, 2)
                            logger.info(f"订单 {order_number}: 电商反推租金，periods={periods}, base={base}, remainder={remainder}, total={total_receivable}, dp_total={dp_total}")
                        elif periods > 0 and total_receivable is not None:
                            # 新逻辑：租金总额 = 总待收 - 买断金（不扣首付）
                            rent_total = float(total_receivable) - (buyout_total or 0.0)
                            if rent_total < 0:
                                logger.warning(f"订单 {order_number}: 计算得到租金总额为负({rent_total})，置0并记账")
                                rent_total = 0.0
                            # 均分到每期
                            base = round(rent_total / periods, 2)
                            # 尾差抹平到最后一期
                            remainder = round(rent_total - base * periods, 2)
                            for p in range(1, periods + 1):
                                expected_amounts[p] = base
                            if remainder != 0 and periods >= 1:
                                expected_amounts[periods] = round(expected_amounts[periods] + remainder, 2)
                            logger.info(f"订单 {order_number}: 新规则生成租金，periods={periods}, base={base}, remainder={remainder}, buyout={buyout_total}")
                        else:
                            # 无法按新规则计算，回退：若有Excel每期金额就用它
                            if payment_amount is not None and periods > 0:
                                for p in range(1, periods + 1):
                                    expected_amounts[p] = float(payment_amount)
                                logger.info(f"订单 {order_number}: 缺少总待收/期数，回退使用Excel每期金额={payment_amount}")

                        # 4+2 买断金拆分：不在账单中扣首付，买断金均分到第5/6期
                        if is_lease and periods == 4 and buyout_total is not None:
                            half = buyout_total / 2.0
                            expected_amounts[5] = half
                            expected_amounts[6] = half
                            logger.info(f"订单 {order_number}: 4+2买断拆分（不扣首付），p5={half}, p6={half}, buyout={buyout_total}")
                    except Exception as e:
                        logger.warning(f"订单 {order_number}: 新规则计算逐期应还金额失败: {e}")

                    created_periods = set()

                    # 添加还款计划（基于Excel提供的列）
                    for col in schedule_cols:
                        date_raw = r.get(col)
                        if pd.isnull(date_raw):
                            logger.debug('订单 %s: 列 %s 日期为空，跳过', order_number, col)
                            continue
                            
                        # 尝试转换日期
                        ts = pd.to_datetime(date_raw, errors='coerce')
                        if pd.isnull(ts):
                            logger.warning('订单 %s: 列 %s 日期格式无效 %s，跳过', 
                                          order_number, col, date_raw)
                            continue
                        
                        # 提取期数
                        key = col.replace('第','').replace('期','')
                        if key.isdigit():
                            period = int(key)
                        elif key in cn_map:
                            period = cn_map[key]
                        else:
                            logger.warning('订单 %s: 列 %s 映射期数失败，跳过', order_number, col)
                            continue
                        
                        # 创建还款计划记录
                        logger.info('创建还款计划: 订单 %s(ID=%d) 第 %d 期: 到期 %s, 金额 %s', 
                                   order_number, order.id, period, ts.date(), payment_amount)
                        
                        try:
                            ps = PaymentSchedule(
                                order_id=order.id,
                                period_number=period,
                                due_date=ts.date(),
                                amount=expected_amounts.get(period, payment_amount),
                                status='未还'  # 初始状态设为"未还"
                            )
                            session.add(ps)
                            payment_plans_added += 1
                            schedules_count += 1
                            created_periods.add(period)
                        except Exception as e:
                            failed_schedules.append({
                                'order_number': order_number,
                                'order_id': order.id,
                                'period': period,
                                'due_date': ts.date(),
                                'amount': payment_amount,
                                'column': col,
                                'error': str(e)
                            })
                            logger.error('添加还款计划失败: 订单=%s, 期数=%d, 错误=%s', 
                                        order_number, period, str(e))

                    # 若Excel未提供计划日期但推导出了金额/期次，自动按规则生成
                    if payment_plans_added == 0:
                        try:
                            if expected_amounts and (order.periods or 0) > 0 and order.order_date:
                                gen = 0
                                for p in sorted(expected_amounts.keys()):
                                    if p < 1:
                                        continue
                                    due_dt = order.order_date + relativedelta(months=+p)
                                    ps = PaymentSchedule(
                                        order_id=order.id,
                                        period_number=p,
                                        due_date=due_dt,
                                        amount=expected_amounts.get(p),
                                        status='未还',
                                        auto_inferred=True
                                    )
                                    session.add(ps)
                                    gen += 1
                                if gen > 0:
                                    schedules_count += gen
                                    payment_plans_added += gen
                                    logger.info(f"订单 {order_number}: Excel无计划日期，按推导规则自动生成 {gen} 条还款计划")
                        except Exception as e:
                            logger.warning(f"订单 {order_number}: 自动生成还款计划失败: {e}")

                    logger.info('订单 %s 添加了 %d 条还款计划', order_number, payment_plans_added)

                # 如为新租赁4+2，确保第5/6期计划存在，若Excel未提供则自动补充
                try:
                    product_type_val = (order.product_type or '')
                    is_lease = '租' in str(product_type_val)
                    if is_lease and order.periods == 4:
                        need_add = []
                        if 5 not in created_periods:
                            need_add.append(5)
                        if 6 not in created_periods:
                            need_add.append(6)
                        if need_add:
                            # 基准日期：优先第4期，否则已存在的最大到期日
                            base_date = None
                            # 尝试从原行读取“第4期”
                            if '第4期' in r and pd.notnull(r.get('第4期')):
                                base_date = pd.to_datetime(r.get('第4期'), errors='coerce')
                                if pd.isnull(base_date):
                                    base_date = None
                            if base_date is None:
                                # 查询已创建的该订单计划的最大日期（使用原生SQL避免ORM列不一致问题）
                                try:
                                    max_due = session.execute(
                                        text("SELECT MAX(due_date) FROM payment_schedules WHERE order_id = :oid"),
                                        {"oid": order.id}
                                    ).scalar()
                                    base_date = max_due
                                except Exception:
                                    base_date = None
                            # 若仍无，则用订单日期
                            if base_date is None:
                                base_date = order.order_date or datetime.date.today()
                            # 计算新增期限日期
                            for p in need_add:
                                months_to_add = p - 4
                                due_dt = (base_date + relativedelta(months=+months_to_add)).date() if isinstance(base_date, pd.Timestamp) else (base_date + relativedelta(months=+months_to_add))
                                amount_val = expected_amounts.get(p, payment_amount)
                                try:
                                    ps = PaymentSchedule(
                                        order_id=order.id,
                                        period_number=p,
                                        due_date=due_dt,
                                        amount=amount_val,
                                        status='未还',
                                        auto_inferred=True
                                    )
                                    session.add(ps)
                                    schedules_count += 1
                                    logger.info(f"订单 {order_number}: 自动补充第{p}期计划，日期={due_dt}, 金额={amount_val}")
                                except Exception as e:
                                    logger.error(f"订单 {order_number}: 自动补充第{p}期失败: {e}")
                except Exception as e:
                    logger.warning(f"订单 {order_number}: 新增5/6期补充失败: {e}")
                
                # 每批次提交一次，减少内存占用
                try:
                    session.commit()
                    logger.info(f'批次 {i//batch_size + 1} 提交成功: 已同步 {orders_count} 条订单, {schedules_count} 条还款计划')
                except Exception as e:
                    logger.error(f'批次 {i//batch_size + 1} 提交失败: {str(e)}')
                    session.rollback()
                    raise
            
            logger.info('订单和还款计划同步完成')
            if skipped_duplicates > 0:
                logger.info('跳过了 %d 个重复订单编号', skipped_duplicates)

            # 同步资金流水账
            logger.info('同步资金流水账开始')
            # 再次防御性保证 period_num_int 列存在（应对远端权限/模式差异）
            try:
                ensure_transactions_period_num_int(session)
            except Exception as _e:
                logger.warning(f'预检查 period_num_int 列失败: {_e}')
            df_t = pd.read_excel(excel_path, sheet_name='资金流水账', engine='openpyxl')
            df_t.columns = df_t.columns.str.strip().str.replace('﻿', '')
            df_t['交易金额'] = pd.to_numeric(df_t['交易金额'], errors='coerce')
            df_t = df_t[df_t['交易金额'].notnull()]
            df_t['归属期数'] = df_t['归属期数'].fillna('').astype(str).str.strip()
            logger.info('资金流水账有效记录: %d 条', len(df_t))
            for idx, r in df_t.iterrows():
                order_number_raw = r.get('订单编号')
                order_id = None
                if pd.notnull(order_number_raw):
                    order_number = str(order_number_raw).strip()
                    # 避免在查询期间触发对挂起交易的自动 flush
                    # 防止在列未准备好时过早尝试 INSERT
                    with session.no_autoflush:
                        ord_rec = session.query(Order).filter_by(order_number=order_number).first()
                    if ord_rec:
                        order_id = ord_rec.id
                    else:
                        logger.warning('第 %d 行交易: 订单编号 %s 未找到，order_id 设为 None', idx, order_number)
                else:
                    logger.warning('第 %d 行交易: 订单编号为空，order_id 设为 None', idx)
                date_raw = r.get('日期')
                ts_txn = pd.to_datetime(date_raw, errors='coerce')
                if pd.isnull(ts_txn):
                    logger.warning('交易 %s: 无效日期 %s', r.get('交易订单号'), date_raw)
                    txn_date = None
                else:
                    txn_date = ts_txn.date()
                # 安全处理数值字段，防止字符串插入到数值字段
                def safe_float(value, default=None):
                    """安全转换为浮点数，如果转换失败返回默认值"""
                    if pd.isnull(value):
                        return default
                    try:
                        if isinstance(value, str):
                            # 移除非数字字符
                            clean_str = re.sub(r'[^\d\.\-\+]', '', str(value))
                            if not clean_str or clean_str in ['-', '+', '.']:
                                return default
                            return float(clean_str)
                        return float(value)
                    except (ValueError, TypeError):
                        logger.warning(f'数值转换失败: {value}, 使用默认值 {default}')
                        return default

                def safe_string(value, max_length=None):
                    """安全转换为字符串，处理NaN和长度限制"""
                    if pd.isnull(value):
                        return None
                    str_value = str(value).strip()
                    if max_length and len(str_value) > max_length:
                        logger.warning(f'字符串截断: {str_value} -> {str_value[:max_length]}')
                        return str_value[:max_length]
                    return str_value if str_value else None

                try:
                    trx = Transaction(
                        transaction_date=txn_date,
                        order_id=order_id,
                        customer_name=safe_string(r.get('客户姓名'), 100),
                        model=safe_string(r.get('型号'), 50),
                        customer_attribute=safe_string(r.get('客户属性'), 50),
                        usage=safe_string(r.get('用途'), 100),
                        payment_cycle=safe_string(r.get('还款周期'), 50),
                        product_type=safe_string(r.get('产品'), 50),
                        amount=safe_float(r.get('交易金额'), 0.0),
                        period_number=safe_string(r.get('归属期数'), 20),
                        period_num_int=extract_period_number(r.get('归属期数')),
                        transaction_type=safe_string(r.get('交易类型'), 50),
                        direction=safe_string(r.get('资金流向'), 50),
                        transaction_order_number=safe_string(r.get('交易订单号'), 100),
                        available_balance=safe_float(r.get('可用余额')),
                        pending_withdrawal=safe_float(r.get('待提现余额'), 0.0),
                        remarks=safe_string(r.get('备注'), 500)  # 备注字段限制500字符
                    )
                    session.add(trx)
                    transactions_count += 1
                except Exception as e:
                    failed_transactions.append({
                        'row': idx,
                        'transaction_order_number': r.get('交易订单号'),
                        'order_number': order_number_raw,
                        'amount': r.get('交易金额'),
                        'error': str(e),
                        'data': dict(r)
                    })
                    logger.error('第 %d 行交易导入失败: 交易订单号=%s, 错误=%s', 
                                idx, r.get('交易订单号'), str(e))
            session.commit()

            # 同步 @芳会资料补充
            logger.info('同步客户信息开始')
            df_c = pd.read_excel(excel_path, sheet_name='@芳会资料补充', engine='openpyxl')
            logger.info('客户信息记录: %d 条', len(df_c))
            for idx, r in df_c.iterrows():
                order_number_raw = r.get('订单编号')
                if pd.isnull(order_number_raw) or not str(order_number_raw).strip():
                    logger.warning('第 %d 行客户信息: 订单编号为空，跳过', idx)
                    continue
                order_number = str(order_number_raw).strip()
                order_obj = session.query(Order).filter_by(order_number=order_number).first()
                if not order_obj:
                    failed_customers.append({
                        'row': idx,
                        'order_number': order_number,
                        'error': 'Order not found',
                        'data': dict(r)
                    })
                    logger.warning('第 %d 行客户信息: 订单编号 %s 未找到，跳过', idx, order_number)
                    continue
                    
                try:
                    ci = session.query(CustomerInfo).filter_by(order_number=order_number).first() or CustomerInfo()
                    ci.order_id = order_obj.id
                    ci.order_number = order_number
                    ci.customer_name = r.get('客户')
                    ci.phone = str(r.get('手机号码')).strip() if pd.notnull(r.get('手机号码')) else None
                    ci.rental_period = str(r.get('租期')).strip() if pd.notnull(r.get('租期')) else None
                    ci.customer_service = str(r.get('客服')).strip() if pd.notnull(r.get('客服')) else None
                    ci.business_affiliation = str(r.get('业务')).strip() if pd.notnull(r.get('业务')) else None
                    ci.remarks = r.get('备注')
                    session.merge(ci)
                    customers_count += 1
                    logger.info('客户信息 %s 已同步', order_number)
                except Exception as e:
                    failed_customers.append({
                        'row': idx,
                        'order_number': order_number,
                        'error': str(e),
                        'data': dict(r)
                    })
                    logger.error('第 %d 行客户信息导入失败: 订单编号=%s, 错误=%s', 
                                idx, order_number, str(e))
            session.commit()

            # 导入摘要和对比统计
            logger.info('=' * 80)
            logger.info('📊 导入结果对比统计:')
            logger.info('  订单: Excel=%d, 导入=%d, 差异=%d', 
                       valid_orders_excel, orders_count, valid_orders_excel - orders_count)
            logger.info('  还款计划: Excel=%d, 导入=%d, 差异=%d', 
                       total_schedules_excel, schedules_count, total_schedules_excel - schedules_count)
            logger.info('  交易: Excel=%d, 导入=%d, 差异=%d', 
                       valid_transactions_excel, transactions_count, valid_transactions_excel - transactions_count)
            logger.info('  客户信息: Excel=%d, 导入=%d, 差异=%d', 
                       valid_customers_excel, customers_count, valid_customers_excel - customers_count)
            logger.info('=' * 80)
            
            # 失败统计日志
            if failed_orders:
                logger.error('订单导入失败: %d 条', len(failed_orders))
                for fail in failed_orders:
                    logger.error('  失败订单: 行%d, 订单号=%s, 错误=%s', 
                                fail['row'], fail['order_number'], fail['error'])
            
            if failed_schedules:
                logger.error('还款计划导入失败: %d 条', len(failed_schedules))
                for fail in failed_schedules:
                    logger.error('  失败还款计划: 订单=%s, 期数=%d, 错误=%s', 
                                fail['order_number'], fail['period'], fail['error'])
            
            if failed_transactions:
                logger.error('交易记录导入失败: %d 条', len(failed_transactions))
                for fail in failed_transactions:
                    logger.error('  失败交易: 行%d, 交易订单号=%s, 错误=%s', 
                                fail['row'], fail['transaction_order_number'], fail['error'])
            
            if failed_customers:
                logger.error('客户信息导入失败: %d 条', len(failed_customers))
                for fail in failed_customers:
                    logger.error('  失败客户信息: 行%d, 订单号=%s, 错误=%s', 
                                fail['row'], fail['order_number'], fail['error'])

            # 返回导入计数
            return orders_count, schedules_count, transactions_count, customers_count
            
        except sqlalchemy.exc.OperationalError as e:
            # 检查是否为锁定错误
            if "database is locked" in str(e):
                retry_count += 1
                if retry_count <= max_retries:
                    logger.warning(f"数据库锁定，第 {retry_count}/{max_retries} 次重试，等待 {retry_interval} 秒...")
                    session.rollback()  # 回滚当前事务
                    time.sleep(retry_interval)  # 等待一段时间后重试
                    retry_interval *= 2  # 使用指数退避策略增加等待时间
                else:
                    logger.error(f"数据库锁定，重试 {max_retries} 次后仍然失败")
                    session.rollback()
                    raise
            else:
                # 其他类型的 OperationalError
                logger.error(f"同步数据库出错: {str(e)}")
                session.rollback()
                raise
        except Exception as e:
            logger.error(f"同步数据库出错: {str(e)}")
            session.rollback()
            raise

def update_payment_status_and_receivable(session):
    """
    更新还款计划状态、订单当前待收和订单状态
    """
    logger.info('开始更新还款状态、当前待收和订单状态')
    
    today = datetime.date.today()
    
    # 1. 获取所有还款计划并预取相关交易，减少 N+1
    payments = session.query(PaymentSchedule).all()
    order_ids = list({p.order_id for p in payments if p.order_id})
    repay_types = ["首付款", "租金", "尾款"]
    txns_by_order = {oid: [] for oid in order_ids}
    if order_ids:
        for t in session.query(Transaction).filter(
            Transaction.order_id.in_(order_ids),
            Transaction.transaction_type.in_(repay_types)
        ).all():
            txns_by_order.setdefault(t.order_id, []).append(t)
    
    # 构建期数匹配条件，使用缓存减少重复计算
    def build_period_filter(period_number):
        # 缓存已计算的期数过滤条件
        if not hasattr(build_period_filter, 'cache'):
            build_period_filter.cache = {}
            
        if period_number in build_period_filter.cache:
            return build_period_filter.cache[period_number]
            
        # 构建可能的期数表示方式列表
        period_str_variations = [
            str(period_number),                 # "1"
            f"第{period_number}期",             # "第1期"
            f"{period_number}期",               # "1期"
            f" {period_number} ",               # " 1 "（带空格）
            f"第 {period_number} 期"            # "第 1 期"（带空格）
        ]
        
        # 中文数字映射（适用于第一期到第十期）
        cn_numbers = {1: '一', 2: '二', 3: '三', 4: '四', 5: '五', 
                      6: '六', 7: '七', 8: '八', 9: '九', 10: '十'}
        if 1 <= period_number <= 10:
            period_str_variations.extend([
                f"第{cn_numbers[period_number]}期",     # "第一期"
                f"{cn_numbers[period_number]}期",       # "一期"
                f"第 {cn_numbers[period_number]} 期"    # "第 一 期"
            ])
            
        # 新租赁 4+2 的别名：买一/买二（文字类），M1/M2（数字类）
        if period_number == 5:
            period_str_variations.extend(["买一", "M1", "m1"])
        if period_number == 6:
            period_str_variations.extend(["买二", "M2", "m2"])

        # 使用 OR 条件匹配多种可能的期数表示
        filter_condition = or_(
            *[Transaction.period_number.like(f"%{pattern}%") for pattern in period_str_variations],
            *[Transaction.period_number == pattern for pattern in period_str_variations]
                    )
        
        # 缓存结果
        build_period_filter.cache[period_number] = filter_condition
        return filter_condition
    
    # 预计算每个订单的最后一期 period_number（用于将首付款抵扣到最后一期）
    last_period_by_order = {}
    for p in payments:
        if p.order_id is None or p.period_number is None:
            continue
        last_period_by_order[p.order_id] = max(last_period_by_order.get(p.order_id, 0), p.period_number)

    # 2. 更新每个还款计划的状态
    for payment in payments:
        order = session.query(Order).filter(Order.id == payment.order_id).first()
        if not order:
            logger.warning('找不到订单 ID: %s', payment.order_id)
            continue
        
        order_number = order.order_number
        
        # 基于预取交易筛选本期相关交易
        all_txns = sorted(txns_by_order.get(payment.order_id, []), key=lambda t: t.transaction_date or datetime.date(1900,1,1))
        period_transactions = []
        for t in all_txns:
            ttype = t.transaction_type or ''
            if ttype == '首付款':
                # 新逻辑：首付抵扣订单的最后一期
                last_p = last_period_by_order.get(payment.order_id)
                if last_p is not None and payment.period_number == last_p:
                    period_transactions.append(t)
            elif ttype in ['租金', '尾款']:
                n = t.period_num_int if hasattr(t, 'period_num_int') and t.period_num_int is not None else _normalize_period_to_int(t.period_number or '')
                if n == payment.period_number:
                    period_transactions.append(t)
        
        logger.debug('订单 %s 第 %s 期: 找到 %d 笔相关交易', 
                    order_number, payment.period_number, len(period_transactions))
        
        # 更精确地计算该期已还金额
        # 如果是第一期，还需要考虑首付款
        total_paid = 0
        if period_transactions:
            for transaction in period_transactions:
                total_paid += (transaction.amount or 0)
        
        # 更新已还金额
        payment.paid_amount = total_paid
        
        # 分析期数格式情况
        has_text_period_format = False
        period_formats = []
        last_payment_date = None
        
        if period_transactions:
            last_payment_date = period_transactions[-1].transaction_date
            for transaction in period_transactions:
                period_str = transaction.period_number if transaction.period_number else ""
                period_formats.append(period_str)
                if _is_textual_period(period_str):
                    has_text_period_format = True
                    break
        
        # 计算差额
        difference = payment.amount - total_paid if payment.amount is not None else 0
        payment.delta_amount = difference
        
        # 简化判断逻辑：
        # 1. 有文字类型账单归属
        #    a) 差值在100以内：正常还款（提前/逾期/按时）
        #    b) 差值超过100：协商结清
        # 2. 无文字类型账单归属（只有数字）
        #    a) 未到期：未到期
        #    b) 到期：逾期未还/账单日
        if has_text_period_format:
            # 有文字类型账单归属
            if abs(difference) <= 100:
                # 差值在100以内，按照正常还款处理
                if last_payment_date > payment.due_date:
                    payment.status = "逾期还款"
                elif last_payment_date < payment.due_date:
                    payment.status = "提前还款"
                else:
                    payment.status = "按时还款"
                logger.debug('订单 %s 第 %s 期正常还款完成，状态: %s，差额: %.2f', 
                            order_number, payment.period_number, payment.status, difference)
            else:
                # 差值超过100，判定为协商结清
                payment.status = "协商结清"
                logger.debug('订单 %s 第 %s 期协商结清，差额: %.2f, 已还: %.2f', 
                            order_number, payment.period_number, difference, total_paid)
        else:
            # 无文字类型账单归属（只有数字或无记录）
            tolerance = 100
            settled = abs(difference) <= tolerance
            if settled and last_payment_date:
                if last_payment_date > payment.due_date:
                    payment.status = "逾期还款"
                elif last_payment_date < payment.due_date:
                    payment.status = "提前还款"
                else:
                    payment.status = "按时还款"
            else:
                if today > payment.due_date:
                    # 逾期场景下区分部分还款
                    if 0 < total_paid < (float(payment.amount or 0) - tolerance):
                        payment.status = "部分还款"
                    else:
                        payment.status = "逾期未还"
                elif today == payment.due_date:
                    payment.status = "账单日"
                else:
                    payment.status = "未到期"
            
            logger.debug('订单 %s 第 %s 期未完成，状态: %s，已还: %.2f，应还: %.2f', 
                        order_number, payment.period_number, payment.status,
                        total_paid, payment.amount or 0)
    
    # 3. 计算每个订单的当前待收
    orders = session.query(Order).all()
    for order in orders:
        # 总待收
        total_receivable = order.total_receivable or 0
        
        # 已收款总额（首付款+租金+尾款）
        received = session.query(func.sum(Transaction.amount)).filter(
            Transaction.order_id == order.id,
            Transaction.transaction_type.in_(["首付款", "租金", "尾款"])
        ).scalar() or 0
        
        # 更新当前待收
        order.current_receivable = total_receivable - received
        
        # 更新成本
        cost = session.query(func.sum(func.abs(Transaction.amount))).filter(
            Transaction.order_id == order.id,
            Transaction.transaction_type.in_(["放款", "供应商利润"])
        ).scalar() or 0
        order.cost = cost
        
        logger.debug('订单 %s 当前待收: %.2f，总待收: %.2f，已收: %.2f', 
                    order.order_number, order.current_receivable, total_receivable, received)
        
        # 4. 更新订单状态
        # 检查订单是否有 status 列，如果没有则添加
        try:
            # 先检查是否存在一行数据
            first_order = session.query(Order).first()
            if first_order is not None:
                # 检查这行数据是否有 status 属性
                hasattr_status = hasattr(first_order, 'status')
                logger.info(f"订单表是否有 status 属性: {hasattr_status}")
                if not hasattr_status:
                    # 如果没有 status 属性，尝试添加
                    logger.info('订单表没有 status 列，尝试添加')
                    # 通过 engine.execute 直接执行 SQL
                    engine = session.get_bind()
                    try:
                        engine.execute(text('ALTER TABLE orders ADD COLUMN status VARCHAR(20)'))
                        logger.info('成功添加 status 列到订单表')
                    except Exception as e:
                        logger.warning(f'添加 status 列时出错: {str(e)}，这可能是因为该列已存在')
                    
                    # 更新统计信息
                    completed_count = session.query(Order).filter(Order.status == '已完成').count()
                    in_progress_count = session.query(Order).filter(Order.status == '在途').count()
                    overdue_count = session.query(Order).filter(Order.status == '逾期').count()
                    logger.info(f"已完成={completed_count}, 在途={in_progress_count}, 逾期={overdue_count}")
        except Exception as e:
            logger.error(f"检查或添加 status 列时出错: {str(e)}")
            
        # 更新订单状态
        # 检查订单的所有付款计划
        all_payment_schedules = order.payment_schedules
        
        # 默认状态为"在途"
        new_status = '在途'
        
        # 检查是否有逾期未还的付款计划
        has_overdue = False
        all_completed = True
        
        for ps in all_payment_schedules:
            ps_status = ps.status or ""
            
            # 检查是否有逾期未还
            if "逾期未还" in ps_status:
                has_overdue = True
                all_completed = False
                break
            
            # 检查是否已完成
            if not any(status in ps_status for status in ["提前还款", "按时还款", "逾期还款", "协商结清"]):
                all_completed = False
        
        # 根据检查结果设置状态
        if has_overdue:
            new_status = '逾期'
        elif all_completed and all_payment_schedules:  # 确保有付款计划且全部完成
            new_status = '完结'
        else:
            new_status = '在途'
        
        # 更新订单状态
        if order.status != new_status:
            order.status = new_status
            logger.debug('更新订单 %s 状态: %s', order.order_number, new_status)
    
    # 确保所有更改被提交到数据库
    session.commit()
    
    # 统计各状态的订单数量
    status_counts = {
        '在途': session.query(Order).filter(Order.status == '在途').count(),
        '完结': session.query(Order).filter(Order.status == '完结').count(),
        '逾期': session.query(Order).filter(Order.status == '逾期').count()
    }
    
    logger.info('订单状态统计: 在途=%d, 完结=%d, 逾期=%d', 
               status_counts['在途'], status_counts['完结'], status_counts['逾期'])
    logger.info('还款状态、当前待收和订单状态更新完成')

def update_order_status(session):
    """
    更新订单状态
    
    订单状态规则：
    - 在途：没有还完的订单，不包含账单为"逾期未还"的订单
    - 完结：所有账单状态都是"提前还款"、"按时还款"、"逾期还款"或"协商结清"
    - 逾期：包含至少一个"逾期未还"状态账单的订单
    
    Args:
        session: 数据库会话
    """
    logger.info('开始更新订单状态')
    
    # 查询所有订单
    orders = session.query(Order).all()
    logger.info('查询到 %d 条订单', len(orders))
    
    # 更新订单状态的计数器
    completed_count = 0  # 完结
    in_progress_count = 0  # 在途
    overdue_count = 0  # 逾期
    
    # 遍历所有订单
    for order in orders:
        # 获取订单的所有还款计划
        payment_schedules = order.payment_schedules
        
        # 如果没有还款计划，设为在途
        if not payment_schedules:
            order.status = '在途'
            in_progress_count += 1
            continue
        
        # 可能的还款状态
        completed_statuses = ['提前还款', '按时还款', '逾期还款', '协商结清']
        
        # 检查是否有任何逾期未还的计划
        has_overdue = False
        all_completed = True
        
        for ps in payment_schedules:
            ps_status = ps.status or ''
            
            # 检查是否有逾期未还
            if '逾期未还' in ps_status:
                has_overdue = True
                all_completed = False
                break
            
            # 检查是否已完成
            if not any(status in ps_status for status in completed_statuses):
                all_completed = False
        
        # 确定订单状态
        if has_overdue:
            order.status = '逾期'
            overdue_count += 1
        elif all_completed:
            order.status = '完结'
            completed_count += 1
        else:
            order.status = '在途'
            in_progress_count += 1
    
    # 提交更改
    session.commit()
    logger.info('订单状态更新完成: 完结=%d, 在途=%d, 逾期=%d', 
               completed_count, in_progress_count, overdue_count)

def update_financial_fields(session):
    """
    更新订单的财务字段：已还金额、逾期本金、当前待收
    
    计算逻辑：
    - 已还金额 = 订单所有"首付款"、"租金"、"尾款"类型交易的金额之和
    - 逾期本金 = 成本 - 已还金额
    - 当前待收 = 应收账单(total_receivable) - 已还金额
    
    Args:
        session: 数据库会话
    """
    logger.info('开始更新订单财务字段：已还金额、逾期本金、当前待收...')
    
    # 查询所有订单
    orders = session.query(Order).all()
    order_count = len(orders)
    logger.info(f'共找到 {order_count} 个订单需要更新财务字段')
    
    # 分批处理，每批100个订单
    batch_size = 100
    processed_count = 0
    
    for i in range(0, order_count, batch_size):
        batch_orders = orders[i:i+batch_size]
        batch_order_ids = [order.id for order in batch_orders]
        
        # 查询这批订单的所有交易记录
        transactions = session.query(Transaction).filter(
            Transaction.order_id.in_(batch_order_ids),
            Transaction.transaction_type.in_(['首付款', '租金', '尾款'])
        ).all()
        
        # 按订单ID分组交易记录
        order_transactions = {}
        for transaction in transactions:
            if transaction.order_id not in order_transactions:
                order_transactions[transaction.order_id] = []
            order_transactions[transaction.order_id].append(transaction)
        
        # 更新每个订单的财务字段
        for order in batch_orders:
            # 计算已还金额
            repaid_amount = 0.0
            if order.id in order_transactions:
                for transaction in order_transactions[order.id]:
                    if transaction.amount:
                        repaid_amount += transaction.amount
            
            # 更新已还金额
            order.repaid_amount = repaid_amount
            
            # 计算逾期本金
            cost = order.cost or 0.0
            order.overdue_principal = max(cost - repaid_amount, 0.0)
            
            # 更新当前待收
            total_receivable = order.total_receivable or 0.0
            order.current_receivable = max(total_receivable - repaid_amount, 0.0)
        
        # 每批提交一次
        session.commit()
        
        processed_count += len(batch_orders)
        logger.info(f'已处理 {processed_count}/{order_count} 个订单的财务字段')
    
    logger.info('订单财务字段更新完成')

def create_performance_indexes(engine):
    """
    创建性能优化索引，确保数据库查询性能
    
    创建的索引包括：
    - PaymentSchedule表上的逾期状态索引
    - Order表上的店铺日期业务类型综合索引
    - Transaction表上的日期订单类型索引
    - Order表上的状态日期索引
    - PaymentSchedule表上的状态金额索引
    - 以及针对新增财务字段的索引
    
    Args:
        engine: SQLAlchemy 数据库引擎
    """
    logger.info("开始创建性能优化索引...")
    
    # 创建索引的SQL语句列表
    index_statements = [
        # PaymentSchedule表上的索引 - 优化逾期查询
        """
        CREATE INDEX IF NOT EXISTS ix_payment_schedules_overdue_status 
        ON payment_schedules (status) 
        WHERE status LIKE '%逾期未还%'
        """,
        
        # Order表上的组合索引 - 优化店铺汇总查询
        """
        CREATE INDEX IF NOT EXISTS ix_orders_shop_date_aggregate 
        ON orders (shop_affiliation, order_date, business_type)
        """,
        
        # Transaction表上的组合索引 - 优化按日期和店铺查询交易
        """
        CREATE INDEX IF NOT EXISTS ix_transactions_date_order_type
        ON transactions (transaction_date, order_id, transaction_type)
        """,

        # 交易表：按订单+标准期次+交易类型，优化按期聚合
        """
        CREATE INDEX IF NOT EXISTS ix_transactions_order_period_type
        ON transactions (order_id, period_num_int, transaction_type)
        """,
        
        # Order表上的索引 - 优化状态查询
        """
        CREATE INDEX IF NOT EXISTS ix_orders_status_date
        ON orders (status, order_date)
        """,
        
        # PaymentSchedule表上的索引 - 优化还款计划查询性能
        """
        CREATE INDEX IF NOT EXISTS ix_payment_schedules_status_amount
        ON payment_schedules (status, amount)
        """,
        
        # 针对新增财务字段的索引
        """
        CREATE INDEX IF NOT EXISTS ix_orders_repaid_amount
        ON orders (repaid_amount)
        """,
        
        """
        CREATE INDEX IF NOT EXISTS ix_orders_overdue_principal
        ON orders (overdue_principal)
        """,
        
        # 订单状态和逾期本金的组合索引 - 优化逾期金额统计查询
        """
        CREATE INDEX IF NOT EXISTS ix_orders_status_overdue_principal
        ON orders (status, overdue_principal)
        WHERE status = '逾期'
        """
    ]
    
    connection = engine.connect()
    try:
        # 开始事务
        trans = connection.begin()
        
        # 执行每条索引创建语句
        for idx, statement in enumerate(index_statements):
            try:
                logger.info(f"创建索引 #{idx+1}...")
                connection.execute(text(statement))
                logger.info(f"索引 #{idx+1} 创建成功")
            except SQLAlchemyError as e:
                logger.error(f"创建索引 #{idx+1} 时出错: {str(e)}")
                
        # 提交事务
        trans.commit()
        logger.info("所有索引创建完成")
        
    except Exception as e:
        logger.error(f"创建索引过程中出错: {str(e)}")
        if 'trans' in locals() and trans:
            trans.rollback()
    finally:
        connection.close()

def run_etl(db_uri=DB_URI, excel_path=EXCEL_PATH):
    """运行 ETL 过程，可由外部调用"""
    # 热加载定价配置，确保每次运行读取最新 pricing.csv
    try:
        global PRICING_CONFIG
        PRICING_CONFIG = load_pricing_config()
        logger.info(f"本次ETL已热加载 pricing 配置: {len(PRICING_CONFIG)} 条")
    except Exception as e:
        logger.warning(f"热加载 pricing 配置失败: {e}")
    # 首先确保数据库表结构是最新的
    engine = create_engine(db_uri, echo=False)
    # 这将创建所有表，如果表已存在，则保持不变
    Base.metadata.create_all(engine)
    
    # 手动检查并添加列（如果不存在）
    try:
        with engine.connect() as conn:
            # PostgreSQL检查列是否存在的方法
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'orders' AND table_schema = 'public'
            """)).fetchall()
            columns = [row[0] for row in result]
            
            # 如果 devices_count 列不存在，则添加它
            if 'devices_count' not in columns:
                logger.info('添加 devices_count 列到 orders 表')
                conn.execute(text("ALTER TABLE orders ADD COLUMN devices_count INTEGER DEFAULT 1"))
                
            # 如果 status 列不存在，则添加它
            if 'status' not in columns:
                logger.info('添加 status 列到 orders 表')
                conn.execute(text("ALTER TABLE orders ADD COLUMN status VARCHAR(20) DEFAULT '在途'"))
                
            # 如果 repaid_amount 列不存在，则添加它
            if 'repaid_amount' not in columns:
                logger.info('添加 repaid_amount 列到 orders 表')
                conn.execute(text("ALTER TABLE orders ADD COLUMN repaid_amount FLOAT DEFAULT 0.0"))
                
            # 如果 overdue_principal 列不存在，则添加它
            if 'overdue_principal' not in columns:
                logger.info('添加 overdue_principal 列到 orders 表')
                conn.execute(text("ALTER TABLE orders ADD COLUMN overdue_principal FLOAT DEFAULT 0.0"))
            
            # 提交更改
            conn.commit()

            # 检查/添加 transactions.period_num_int 列（稳健方式）
            try:
                logger.info('预检查并添加 transactions.period_num_int 列（IF NOT EXISTS）')
                conn.execute(text("ALTER TABLE IF EXISTS transactions ADD COLUMN IF NOT EXISTS period_num_int INTEGER"))
                conn.commit()
            except Exception as e2:
                logger.warning(f"检查或添加 transactions.period_num_int 列时出错: {str(e2)}")
            
            # 检查/添加 payment_schedules.auto_inferred 列（稳健方式）
            try:
                logger.info('预检查并添加 payment_schedules.auto_inferred 列（IF NOT EXISTS）')
                conn.execute(text("ALTER TABLE IF EXISTS payment_schedules ADD COLUMN IF NOT EXISTS auto_inferred BOOLEAN DEFAULT FALSE"))
                conn.commit()
            except Exception as e3:
                logger.warning(f"检查或添加 payment_schedules.auto_inferred 列时出错: {str(e3)}")
    except Exception as e:
        logger.warning(f"检查或添加列时出错: {str(e)}")
    
    sess = get_session(db_uri)
    try:
        logger.info('开始 ETL 过程，数据库: %s，Excel: %s', db_uri, excel_path)
        
        # 记录详细的导入过程
        orders_count, schedules_count, transactions_count, customers_count = sync_to_db(sess, excel_path)

        # 记录导入摘要
        logger.info('导入摘要: 订单 %d 条, 还款计划 %d 条, 交易 %d 条, 客户信息 %d 条', 
                   orders_count, schedules_count, transactions_count, customers_count)

        # 更新还款状态
        try:
            ensure_payment_schedules_auto_inferred(sess)
        except Exception as _e:
            logger.warning(f'更新前检查 auto_inferred 列失败: {_e}')
        update_payment_status_and_receivable(sess)
        
        # 更新订单状态
        update_order_status(sess)
        
        # 更新财务字段
        update_financial_fields(sess)
        
        # 创建和更新索引，提高查询性能
        create_performance_indexes(engine)
        
        logger.info('ETL 过程完成')
        return True, "ETL 过程成功完成", orders_count, schedules_count, transactions_count, customers_count
    except Exception as e:
        logger.error('ETL 过程失败: %s', str(e), exc_info=True)
        return False, f"ETL 过程失败: {str(e)}", 0, 0, 0, 0
    finally:
        sess.close()


# ----------------------
# Import report builder
# ----------------------

def _is_textual_period(s: str) -> bool:
    if not s:
        return False
    s = str(s)
    # 第X期 / 第一期 等
    if re.search(r'第[一二三四五六七八九十0-9]+期', s):
        return True
    # 买一/买二
    if '买一' in s or '买二' in s:
        return True
    return False

def _extract_period_hint_numbers(s: str):
    """从期数字符串中提取明确期次编号集合，仅限严格匹配，避免子串误判。
    支持：第X期（中文数字/阿拉伯数字）、买一/买二、M1/M2。
    """
    if not s:
        return set()
    s = str(s)
    hints = set()
    # 买一/买二
    if '买一' in s:
        hints.add(5)
    if '买二' in s:
        hints.add(6)
    # M1/M2
    if re.search(r'\b[Mm]1\b', s):
        hints.add(5)
    if re.search(r'\b[Mm]2\b', s):
        hints.add(6)
    # 第X期（中文）
    cn_map = {
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
    }
    m = re.findall(r'第([一二三四五六七八九十]{1,3})期', s)
    for token in m:
        # 仅处理到十的简单组合
        if len(token) == 1:
            n = cn_map.get(token)
        elif token == '十一':
            n = 11
        elif token == '十二':
            n = 12
        else:
            n = None
        if n:
            hints.add(n)
    # 第n期（数字）
    for mm in re.findall(r'第(\d{1,2})期', s):
        try:
            n = int(mm)
            hints.add(n)
        except Exception:
            pass
    return hints

def _normalize_period_to_int(s: str):
    """标准化期数为整数。
    支持：买一/买二 -> 5/6；M1/M2 -> 5/6；第X期（中文/数字）；纯数字或数字+期。
    无法识别返回 None。
    """
    if not s:
        return None
    s = str(s).strip()
    if '买一' in s:
        return 5
    if '买二' in s:
        return 6
    if re.search(r'\b[Mm]1\b', s):
        return 5
    if re.search(r'\b[Mm]2\b', s):
        return 6
    m = re.match(r'^第([一二三四五六七八九十]{1,3})期$', s)
    if m:
        token = m.group(1)
        cn_map = {'一':1,'二':2,'三':3,'四':4,'五':5,'六':6,'七':7,'八':8,'九':9,'十':10}
        if token in cn_map:
            return cn_map[token]
        if token == '十一':
            return 11
        if token == '十二':
            return 12
        return None
    m2 = re.match(r'^第(\d{1,2})期$', s)
    if m2:
        try:
            return int(m2.group(1))
        except Exception:
            return None
    m3 = re.match(r'^(\d{1,2})(期)?$', s)
    if m3:
        try:
            return int(m3.group(1))
        except Exception:
            return None
    return None


def _pricing_match_path(product_type: str, model: str, periods: int):
    """返回 (row, match_path) 其中 match_path in {'exact','alias','default','miss'}"""
    if not PRICING_CONFIG:
        return None, 'miss'
    pt = _normalize_text(product_type)
    mn = _normalize_model(model)
    # exact
    for row in PRICING_CONFIG:
        if row.get('product_type_norm') == pt and row.get('periods_int') == periods and row.get('model_norm_norm') == mn:
            return row, 'exact'
    # alias
    for row in PRICING_CONFIG:
        if row.get('product_type_norm') == pt and row.get('periods_int') == periods and mn in (row.get('model_aliases_norm') or []):
            return row, 'alias'
    # default
    for row in PRICING_CONFIG:
        if row.get('product_type_norm') == pt and row.get('periods_int') == periods:
            return row, 'default'
    return None, 'miss'


def build_import_report(session):
    """Build a structured import report from current DB state.

    Returns: dict with summary, issues, and orders array.
    """
    orders = session.query(Order).all()
    report = {
        'summary': {
            'total_orders': len(orders),
            'total_schedules': 0,
            'contract_check_pass': 0,
            'contract_check_fail': 0,
            'used_pricing_count': 0,
            'used_reverse_calc_count': 0,
        },
        'issues': [],
        'orders': []
    }

    # 预取所有订单的交易，避免 N+1
    order_ids = [o.id for o in orders]
    txns_by_order = {oid: [] for oid in order_ids}
    if order_ids:
        for t in session.query(Transaction).filter(Transaction.order_id.in_(order_ids)).all():
            txns_by_order.setdefault(t.order_id, []).append(t)

    for order in orders:
        schedules = sorted(order.payment_schedules, key=lambda p: (p.period_number or 0))
        report['summary']['total_schedules'] += len(schedules)

        # paid_amount / delta 已在状态更新中写入；若未写入，做保护
        sched_objs = []
        # 集合该订单的所有交易用于 period hint 检测
        txns = txns_by_order.get(order.id, [])
        textual_period_by_num = {}
        for t in txns:
            ps = t.period_number or ''
            if _is_textual_period(ps):
                for n in _extract_period_hint_numbers(ps):
                    textual_period_by_num[n] = True

        auto_inferred_found = False
        for ps in schedules:
            expected = float(ps.amount or 0)
            paid = float(ps.paid_amount or 0)
            delta = float(getattr(ps, 'delta_amount', expected - paid) or 0)
            auto_inferred = bool(getattr(ps, 'auto_inferred', False))
            if auto_inferred:
                auto_inferred_found = True
            sched_objs.append({
                'period': ps.period_number,
                'due_date': ps.due_date.isoformat() if ps.due_date else None,
                'expected_amount': round(expected, 2),
                'paid_amount': round(paid, 2),
                'delta_amount': round(delta, 2),
                'status': ps.status,
                'hint_from_textual_period': bool(textual_period_by_num.get(ps.period_number, False)),
                'auto_inferred': auto_inferred
            })

        # 合同金额一致性校验（若有 total_receivable）
        excel_contract = order.total_receivable
        computed_contract = None
        try:
            rent_sum = sum([p.amount or 0 for p in schedules if p.period_number and order.periods and p.period_number <= order.periods])
            buyout_sum = sum([p.amount or 0 for p in schedules if p.period_number and order.periods and p.period_number > order.periods])
            downpayment_sum = 0.0
            for t in txns:
                if (t.transaction_type or '') == '首付款' and t.amount:
                    downpayment_sum += t.amount
            computed_contract = float(rent_sum + buyout_sum + downpayment_sum)
        except Exception:
            computed_contract = None

        contract_pass = None
        contract_diff = None
        if excel_contract is not None and computed_contract is not None:
            contract_diff = round(abs(float(excel_contract) - computed_contract), 2)
            contract_pass = (contract_diff <= 1.0)
            if contract_pass:
                report['summary']['contract_check_pass'] += 1
            else:
                report['summary']['contract_check_fail'] += 1
                report['issues'].append({
                    'order_number': order.order_number,
                    'type': 'contract_mismatch',
                    'severity': 'warn',
                    'message': f"合同金额与计算不一致: excel={excel_contract} 计算={computed_contract} 差额={contract_diff}"
                })

        # 估算来源与定价命中
        pricing_row, match_path = _pricing_match_path(order.product_type or '', order.model or '', order.periods or 0)
        devices = order.devices_count or 1
        pricing_rent = None
        pricing_dp = None
        pricing_buyout = None
        if pricing_row:
            pricing_rent = pricing_row.get('rent_per_period_float')
            pricing_dp = pricing_row.get('downpayment_default_float')
            pricing_buyout = pricing_row.get('buyout_total_float')
        if pricing_rent is not None:
            pricing_rent = pricing_rent * devices
        if pricing_dp is not None:
            pricing_dp = pricing_dp * devices
        if pricing_buyout is not None:
            pricing_buyout = pricing_buyout * devices

        # 观测 rent 来源：
        # 1) 若前 periods 期金额一致=R，且 R≈pricing_rent → 'pricing'
        # 2) 否则若 excel_contract 存在且 R≈(excel_contract-dp-buyout)/periods → 'reverse'
        # 3) 否则若前 periods 期金额一致 → 'excel'，否则 null
        rent_source = None
        rent_observed = None
        if order.periods and order.periods > 0:
            front = [p.amount or 0 for p in schedules if p.period_number and p.period_number <= order.periods]
            if len(front) == order.periods and all(abs(front[i]-front[0]) < 0.01 for i in range(len(front))):
                rent_observed = float(front[0])
        dp_observed = 0.0
        for t in txns:
            if (t.transaction_type or '') == '首付款' and t.amount:
                dp_observed += float(t.amount)
        buyout_observed = sum([(p.amount or 0) for p in schedules if p.period_number and order.periods and p.period_number > order.periods])

        def approx_equal(a, b, tol=1.0):
            if a is None or b is None:
                return False
            return abs(float(a)-float(b)) <= tol

        if rent_observed is not None and pricing_rent is not None and approx_equal(rent_observed, pricing_rent, 1.0):
            rent_source = 'pricing'
        elif excel_contract is not None and rent_observed is not None and order.periods and order.periods > 0:
            rev = (float(excel_contract) - float(dp_observed) - float(buyout_observed)) / float(order.periods)
            if approx_equal(rev, rent_observed, 1.0):
                rent_source = 'reverse'
        elif rent_observed is not None:
            rent_source = 'excel'

        # dp/buyout 来源粗判
        dp_source = None
        if dp_observed and pricing_dp and approx_equal(dp_observed, pricing_dp, 1.0):
            dp_source = 'pricing'
        elif dp_observed:
            dp_source = 'excel'
        buyout_source = None
        if pricing_buyout and approx_equal(buyout_observed, pricing_buyout, 1.0):
            buyout_source = 'pricing'
        elif buyout_observed:
            buyout_source = 'excel'

        # summary 计数
        if rent_source == 'pricing' or dp_source == 'pricing' or buyout_source == 'pricing':
            report['summary']['used_pricing_count'] += 1
        if rent_source == 'reverse':
            report['summary']['used_reverse_calc_count'] += 1

        # 新 issues：missing_p56 / pricing_miss / period_conflict / partial_payment
        if (order.periods == 4) and auto_inferred_found:
            report['issues'].append({
                'order_number': order.order_number,
                'type': 'missing_p56',
                'severity': 'info',
                'message': '自动补齐了第5/第6期'
            })
        if match_path == 'miss' and (rent_source == 'reverse' or rent_source is None):
            severity = 'warn' if rent_source == 'reverse' else 'error'
            report['issues'].append({
                'order_number': order.order_number,
                'type': 'pricing_miss',
                'severity': severity,
                'message': '定价未命中，使用反推或无法估算'
            })
        for ps in schedules:
            d = float(getattr(ps, 'delta_amount', (ps.amount or 0) - (ps.paid_amount or 0)) or 0)
            if bool(textual_period_by_num.get(ps.period_number or 0, False)) and abs(d) > 100:
                report['issues'].append({
                    'order_number': order.order_number,
                    'type': 'period_conflict',
                    'severity': 'warn',
                    'message': f'期次文本提示与金额冲突: period={ps.period_number}, delta={round(d,2)}'
                })
            if (ps.status or '') == '部分还款':
                report['issues'].append({
                    'order_number': order.order_number,
                    'type': 'partial_payment',
                    'severity': 'info',
                    'message': f'部分还款: period={ps.period_number}'
                })

        report['orders'].append({
            'order_number': order.order_number,
            'product_type': order.product_type,
            'model_raw': order.model,
            'model_norm': _normalize_model(order.model or ''),
            'periods': order.periods,
            'devices': order.devices_count,
            'pricing_match_path': match_path,
            'estimation': {
                'rent': rent_source,
                'dp': dp_source,
                'buyout': buyout_source,
            },
            'contract': {
                'excel': float(excel_contract) if excel_contract is not None else None,
                'computed': computed_contract,
                'diff': contract_diff,
                'pass': contract_pass,
            },
            'schedules': sched_objs,
        })

    return report

if __name__ == '__main__':
    success, message, orders_count, schedules_count, transactions_count, customers_count = run_etl()
    print(message)
