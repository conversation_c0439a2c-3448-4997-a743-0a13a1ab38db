from app import create_app
from app.utils.report_store import make_run_id, save_report


def main():
    app = create_app()
    app.config['TESTING'] = True
    client = app.test_client()

    run_id = make_run_id()
    report = {
        'summary': {},
        'orders': [],
        'issues': [
            {'order_number': 'A1', 'type': 'contract_mismatch', 'severity': 'warn', 'message': 'diff=12.3'},
            {'order_number': 'B2', 'type': 'partial_payment', 'severity': 'info', 'message': 'delta=230'},
        ]
    }
    save_report(run_id, report)

    with client.session_transaction() as sess:
        sess['logged_in'] = True
        sess['username'] = 'tester'

    resp = client.get(f'/api/etl/report/download?run_id={run_id}&format=csv&type=issues')
    print('status:', resp.status_code)
    print(resp.data.decode('utf-8'))


if __name__ == '__main__':
    main()

