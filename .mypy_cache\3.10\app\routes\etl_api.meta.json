{"data_mtime": 1757726484, "dep_lines": [4, 22, 8, 3, 5, 6, 7, 9, 10, 11, 12, 13, 14, 20, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app.routes.auth", "app.utils.report_store", "werkzeug.utils", "flask", "os", "logging", "time", "sys", "re", "uuid", "json", "threading", "queue", "etl", "datetime", "builtins", "_frozen_importlib", "_typeshed", "_typeshed.wsgi", "abc", "flask.blueprints", "flask.scaffold", "typing", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response"], "hash": "75e903e6e06f29463dfa2293e63301cb85639496", "id": "app.routes.etl_api", "ignore_all": true, "interface_hash": "f712770e5dc1aba28845d35cb0076ee14e99b371", "mtime": 1757726450, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\app\\routes\\etl_api.py", "plugin_data": null, "size": 29921, "suppressed": [], "version_id": "1.15.0"}