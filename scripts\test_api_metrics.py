from app import create_app
from app.utils.report_store import make_run_id, save_report


def main():
    app = create_app()
    app.config['TESTING'] = True
    client = app.test_client()

    # Prepare a dummy report with anomalies
    run_id = make_run_id()
    report = {
        'summary': {'total_orders': 2, 'total_schedules': 4, 'contract_check_pass': 1, 'contract_check_fail': 1},
        'orders': [
            {'order_number': 'A1', 'product_type': '租赁', 'schedules': [
                {'period': 1, 'due_date': '2025-01-15', 'expected_amount': 100.0, 'paid_amount': 100.0, 'delta_amount': 0.0, 'status': '按时还款'},
                {'period': 2, 'due_date': '2025-02-15', 'expected_amount': 200.0, 'paid_amount': 50.0, 'delta_amount': 150.0, 'status': '逾期未还'},
            ]},
            {'order_number': 'B2', 'product_type': '电商', 'schedules': [
                {'period': 1, 'due_date': '2025-01-15', 'expected_amount': 300.0, 'paid_amount': 250.0, 'delta_amount': 50.0, 'status': '逾期未还'},
                {'period': 2, 'due_date': '2025-02-15', 'expected_amount': 150.0, 'paid_amount': 150.0, 'delta_amount': 0.0, 'status': '按时还款'},
            ]},
        ],
        'issues': [ {'order_number':'X', 'type':'contract_mismatch', 'severity':'warn', 'message':'diff=10'} ]
    }
    save_report(run_id, report)

    with client.session_transaction() as sess:
        sess['logged_in'] = True
        sess['username'] = 'tester'

    # Latest metrics with threshold 100
    r = client.get('/api/etl/metrics?delta_threshold=100&source=latest')
    print('status:', r.status_code)
    print('json:', r.json)


if __name__ == '__main__':
    main()

