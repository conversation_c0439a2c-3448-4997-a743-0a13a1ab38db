#!/usr/bin/env python3
# start_dev.py - 开发环境启动脚本

import os
import sys

# 设置环境变量
os.environ['FLASK_ENV'] = 'development'
os.environ['FLASK_DEBUG'] = '1'

try:
    from app import create_app
    
    app = create_app()
    
    # 确保在开发模式下运行
    app.config['DEBUG'] = True
    app.config['TEMPLATES_AUTO_RELOAD'] = True
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
    
    print("=" * 50)
    print("Flask 开发服务器启动中...")
    print("模板自动重载: 已启用")
    print("调试模式: 已启用")
    print("访问地址: http://localhost:5000")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True,
        use_debugger=True
    )
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖都已安装")
    sys.exit(1)
except Exception as e:
    print(f"启动错误: {e}")
    sys.exit(1)