# app/routes/db/queries.py
# 数据库查询工具类

import logging
import re
from datetime import datetime, date
import time  # 添加time模块用于性能监控
from sqlalchemy import or_, and_, func, text
from collections import defaultdict

from app.routes.db import get_db_session, close_db_session
from app.routes.db.models import Order, PaymentSchedule, Transaction, CustomerInfo

logger = logging.getLogger(__name__)

def safe_str(value):
    """安全地将值转换为字符串，去除空白"""
    return str(value).strip() if value is not None else ''

def normalize_name(name):
    """去除空白，便于比较"""
    if not name:
        return ''
    return re.sub(r'\s+', '', safe_str(name))

def flexible_name_match(query, target):
    """采用包含及标准化匹配判断两个名字是否匹配"""
    if not query or not target:
        return False
    if safe_str(query) in safe_str(target) or safe_str(target) in safe_str(query):
        return True
    if normalize_name(query) == normalize_name(target):
        return True
    if normalize_name(query) in normalize_name(target) or normalize_name(target) in normalize_name(query):
        return True
    if '白雪' in query or '白雪' in target:
        if '白雪' in safe_str(query) and '白雪' in safe_str(target):
            return True
    return False

class OrderQueries:
    """订单相关查询"""
    
    @staticmethod
    def get_customer_summary(customer_query):
        """
        获取客户订单汇总数据
        
        Args:
            customer_query: 客户姓名或手机号
            
        Returns:
            dict: 客户汇总数据
        """
        # 记录函数开始执行时间
        start_time = time.time()
        logger.info(f"开始执行客户汇总查询，查询参数: {customer_query}")
        
        session = get_db_session()
        try:
            # 初始化汇总数据
            summary = {
                'basic_info': {
                    'customers': set(),
                    'phones': set(),
                    'device_models': set(),
                    'services': set(),
                    'business_affiliations': set(),
                    'total_devices_count': 0,  # 新增：总台数字段
                    'customer_remarks': set(),  # 新增：客户信息备注字段
                    'order_remarks': set(),  # 新增：订单备注信息
                    'total_cost': 0.0  # 新增：成本合计
                },
                'order_summary': {
                    'total_count': 0,  # 这里保留原变量名，但实际代表总台数
                    'total_amount': 0.0,
                    'current_receivable': 0.0,
                    'repaid_amount': 0.0,
                    'business_types': defaultdict(int)
                },
                'receivable_by_periods': defaultdict(lambda: {'count': 0, 'amount': 0.0}),
                'order_details': [],
                'finance_records': []
            }
            
            # 查询匹配的订单
            # 1. 先尝试通过客户姓名匹配
            name_orders = session.query(Order).filter(
                Order.customer_name.like(f"%{customer_query}%")
            ).all()
            
            # 2. 尝试通过手机号匹配
            phone_orders = session.query(Order).join(CustomerInfo).filter(
                CustomerInfo.phone.like(f"%{customer_query}%")
            ).all()
            
            # 合并结果，去重
            matching_orders = list({order.id: order for order in name_orders + phone_orders}.values())
            
            # 记录订单查询时间
            query_time = time.time() - start_time
            logger.info(f"订单查询完成，找到{len(matching_orders)}条记录，耗时: {query_time:.4f}秒")
            
            if not matching_orders:
                logger.info(f"未找到匹配客户: {customer_query}")
                return {'message': '未找到匹配的订单。', 'results': []}
            
            # 处理匹配的订单
            for order in matching_orders:
                # 基本信息
                summary['basic_info']['customers'].add(order.customer_name)
                if order.model:
                    summary['basic_info']['device_models'].add(order.model)
                
                # 从客户信息表获取更多信息
                if order.customer_info:
                    if order.customer_info.phone:
                        summary['basic_info']['phones'].add(order.customer_info.phone)
                    if order.customer_info.customer_service:
                        summary['basic_info']['services'].add(order.customer_info.customer_service)
                    if order.customer_info.business_affiliation:
                        summary['basic_info']['business_affiliations'].add(order.customer_info.business_affiliation)
                    if order.customer_info.remarks:
                        summary['basic_info']['customer_remarks'].add(order.customer_info.remarks)
                
                # 获取台数（如果字段不存在或为空，默认为1）
                devices_count = getattr(order, 'devices_count', 1) or 1
                
                # 更新总台数
                summary['basic_info']['total_devices_count'] += devices_count
                
                # 更新订单汇总统计
                summary['order_summary']['total_count'] += devices_count
                summary['order_summary']['total_amount'] += order.total_receivable or 0
                summary['order_summary']['current_receivable'] += order.current_receivable or 0
                summary['order_summary']['repaid_amount'] += ((order.total_receivable or 0) - (order.current_receivable or 0))
                
                # 更新成本合计
                summary['basic_info']['total_cost'] += order.cost or 0
                
                # 收集订单备注信息
                if order.remarks:
                    summary['basic_info']['order_remarks'].add(order.remarks)
                
                if order.product_type:
                    summary['order_summary']['business_types'][order.product_type] += devices_count
                
                # 计算当前待收期数
                current_receivable_periods = 0
                for ps in order.payment_schedules:
                    if ps.status in ["未到期", "账单日", "逾期未还", "部分还款"]:
                        current_receivable_periods += 1
                
                if current_receivable_periods > 0:
                    key = str(current_receivable_periods)
                    summary['receivable_by_periods'][key]['count'] += 1
                    summary['receivable_by_periods'][key]['amount'] += order.current_receivable or 0
                
                # 订单详情
                order_date_str = order.order_date.strftime("%Y-%m-%d") if order.order_date else ""
                order_detail = {
                    'order_date': order_date_str,
                    'order_number': order.order_number,
                    'product_type': order.product_type,
                    'total_finance': order.total_receivable or 0,
                    'current_receivable': order.current_receivable or 0,
                    'total_periods': order.periods or 0,
                    'current_receivable_periods': current_receivable_periods,
                    'devices_count': order.devices_count or 1,  # 添加台数字段，如果为空则默认为1
                    'cost': order.cost or 0  # 保留成本字段在订单详情中
                }
                summary['order_details'].append(order_detail)
            
            # 处理财务流水数据
            order_numbers = [order.order_number for order in matching_orders]
            transactions = session.query(Transaction).filter(
                and_(
                    Transaction.order_id.in_([order.id for order in matching_orders]),
                    Transaction.transaction_type.in_(["首付款", "租金", "尾款"])
                )
            ).order_by(Transaction.transaction_date).all()
            
            for trans in transactions:
                trans_date_str = trans.transaction_date.strftime("%Y-%m-%d") if trans.transaction_date else ""
                summary['finance_records'].append({
                    'date': trans_date_str,
                    'transaction_type': trans.transaction_type,
                    'amount': trans.amount or 0,
                    'order_number': trans.order.order_number if trans.order else "",
                    'direction': trans.direction or "",  # 添加资金流向字段
                    'transaction_order_number': trans.transaction_order_number or ""  # 添加交易流水号字段
                })
            
            # 整理待收期数数据为列表
            receivable_list = []
            for period, data in summary['receivable_by_periods'].items():
                receivable_list.append({
                    'periods': period,
                    'count': data['count'],
                    'amount': data['amount']
                })
            receivable_list.sort(key=lambda x: int(x['periods']))
            
            # 构造最终汇总数据 - 重构为中文、简洁、按类别分组的结构
            final_summary = {
                '客户基本信息': {
                    '姓名': list(summary['basic_info']['customers']),
                    '手机号': list(summary['basic_info']['phones']),
                    '设备型号': list(summary['basic_info']['device_models']),
                    '客服人员': list(summary['basic_info']['services']),
                    '业务归属': list(summary['basic_info']['business_affiliations']),
                    '客户备注': list(summary['basic_info']['customer_remarks']),
                    '订单备注': list(summary['basic_info']['order_remarks'])
                },
                '订单汇总': {
                    '总台数': summary['basic_info']['total_devices_count'],
                    '总成本': round(summary['basic_info']['total_cost'], 2),
                    '总融资金额': summary['order_summary']['total_amount'],
                    '当前待收金额': summary['order_summary']['current_receivable'],
                    '已还款金额': summary['order_summary']['repaid_amount'],
                    '业务类型分布': dict(summary['order_summary']['business_types'])
                },
                '待收期数统计': receivable_list,
                '订单明细': [{
                    '订单日期': detail['order_date'],
                    '订单编号': detail['order_number'],
                    '产品类型': detail['product_type'],
                    '融资总额': detail['total_finance'],
                    '当前待收': detail['current_receivable'],
                    '总期数': detail['total_periods'],
                    '待收期数': detail['current_receivable_periods'],
                    '台数': detail['devices_count'],
                    '成本': detail['cost']
                } for detail in summary['order_details']],
                '资金流水': [{
                    '交易日期': record['date'],
                    '交易类型': record['transaction_type'],
                    '金额': record['amount'],
                    '订单编号': record['order_number'],
                    '资金流向': record['direction'],
                    '交易流水号': record['transaction_order_number']
                } for record in sorted(summary['finance_records'], key=lambda x: x['date'])]
            }
            
            logger.info(f"客户汇总数据处理完成，订单数量: {summary['order_summary']['total_count']}")
            return [{'汇总数据': final_summary}]
        
        except Exception as e:
            logger.error(f"客户汇总查询异常: {e}")
            raise
        finally:
            close_db_session(session)
            # 记录总执行时间
            total_time = time.time() - start_time
            logger.info(f"客户汇总查询完成，总耗时: {total_time:.4f}秒")

    @classmethod
    def get_order_summary(cls, end_date):
        """
        获取订单汇总数据，按月统计电商订单和租赁订单数量
        
        Args:
            end_date: 结束日期
            
        Returns:
            list: 按月统计的订单数据
        """
        # 记录函数开始执行时间
        start_time = time.time()
        logger.info(f"开始执行订单汇总查询，结束日期: {end_date}")
        
        session = get_db_session()
        try:
            # 查找最早的订单日期
            earliest_date = session.query(func.min(Order.order_date)).scalar()
            
            if not earliest_date:
                logger.warning("未找到有效的订单日期。")
                return []
            
            # 若结束日期早于最早日期，说明无统计数据
            if end_date < earliest_date:
                return []
            
            # 生成所有目标月份
            months_to_track = []
            current_date = datetime(earliest_date.year, earliest_date.month, 1).date()
            while current_date <= end_date:
                month_key = current_date.strftime('%Y-%m')
                months_to_track.append(month_key)
                # 增加一个月
                current_month = current_date.month + 1
                current_year = current_date.year
                if current_month > 12:
                    current_month = 1
                    current_year += 1
                current_date = datetime(current_year, current_month, 1).date()
            
            # 初始化统计字典
            monthly_stats = {
                month: {"电商台数": 0, "租赁台数": 0} 
                for month in months_to_track
            }
            
            # 查询订单数据
            orders = session.query(Order).filter(
                and_(
                    Order.order_date >= earliest_date,
                    Order.order_date <= end_date
                )
            ).all()
            
            # 按月份统计订单
            for order in orders:
                if not order.order_date:
                    continue
                
                month_key = order.order_date.strftime('%Y-%m')
                if month_key not in monthly_stats:
                    continue
                
                # 获取台数（如果字段不存在或为空，默认为1）
                devices_count = getattr(order, 'devices_count', 1) or 1
                
                # 根据产品类型确定订单类型
                product_type = (order.product_type or "").lower().strip()
                
                if "电商" in product_type:
                    monthly_stats[month_key]["电商台数"] += devices_count
                elif "租赁" in product_type or "租" in product_type:
                    monthly_stats[month_key]["租赁台数"] += devices_count
                elif order.shop_affiliation and "租" in order.shop_affiliation.lower():
                    # 根据店铺名称判断
                    monthly_stats[month_key]["租赁台数"] += devices_count
                else:
                    # 默认归为电商
                    monthly_stats[month_key]["电商台数"] += devices_count
            
            # 构建返回结果
            summary_data = []
            for month in sorted(months_to_track):
                stats = monthly_stats[month]
                summary_data.append({
                    "月份": month,
                    "电商台数": stats["电商台数"],
                    "租赁台数": stats["租赁台数"]
                })
            
            logger.info(f"订单汇总数据处理完成，时间范围: {earliest_date} 至 {end_date}")
            return summary_data
        
        except Exception as e:
            logger.error(f"获取订单汇总数据出错: {str(e)}")
            logger.exception("详细错误信息")
            raise
        finally:
            close_db_session(session)
            # 记录总执行时间
            total_time = time.time() - start_time
            logger.info(f"订单汇总查询完成，总耗时: {total_time:.4f}秒")
    
    @classmethod
    def get_overdue_summary(cls, end_date):
        """
        获取逾期订单汇总数据，按月统计电商逾期订单和租赁逾期订单数量 - 高性能版本
        
        Args:
            end_date: 结束日期
            
        Returns:
            list: 按月统计的逾期订单数据
        """
        start_time = time.time()
        logger.info(f"开始执行逾期订单汇总查询（高性能版本），结束日期: {end_date}")
        
        session = get_db_session()
        try:
            from sqlalchemy import text
            
            # 直接使用orders.status='逾期'进行简单高效的统计
            query = text("""
                SELECT 
                    TO_CHAR(o.order_date, 'YYYY-MM') as month_key,
                    CASE 
                        WHEN LOWER(COALESCE(o.business_type, o.product_type, '')) LIKE '%电商%' THEN '电商'
                        WHEN LOWER(COALESCE(o.business_type, o.product_type, '')) LIKE '%租赁%' 
                             OR LOWER(COALESCE(o.business_type, o.product_type, '')) LIKE '%租%' THEN '租赁'
                        WHEN LOWER(COALESCE(o.shop_affiliation, '')) LIKE '%租%' THEN '租赁'
                        ELSE '电商'
                    END as business_category,
                    SUM(COALESCE(o.devices_count, 1)) as total_count
                FROM orders o
                WHERE o.status = '逾期'
                    AND o.order_date <= :end_date
                GROUP BY 
                    TO_CHAR(o.order_date, 'YYYY-MM'),
                    CASE 
                        WHEN LOWER(COALESCE(o.business_type, o.product_type, '')) LIKE '%电商%' THEN '电商'
                        WHEN LOWER(COALESCE(o.business_type, o.product_type, '')) LIKE '%租赁%' 
                             OR LOWER(COALESCE(o.business_type, o.product_type, '')) LIKE '%租%' THEN '租赁'
                        WHEN LOWER(COALESCE(o.shop_affiliation, '')) LIKE '%租%' THEN '租赁'
                        ELSE '电商'
                    END
                ORDER BY month_key, business_category;
            """)
            
            result = session.execute(query, {'end_date': end_date})
            
            # 收集统计结果
            monthly_stats = defaultdict(lambda: {"电商逾期订单数量": 0, "租赁逾期订单数量": 0})
            
            for row in result:
                month_key = row.month_key
                business_category = row.business_category
                total_count = int(row.total_count or 0)
                
                if business_category == '电商':
                    monthly_stats[month_key]["电商逾期订单数量"] = total_count
                elif business_category == '租赁':
                    monthly_stats[month_key]["租赁逾期订单数量"] = total_count
            
            # 构建返回结果（只返回有数据的月份）
            summary_data = []
            for month_key in sorted(monthly_stats.keys()):
                summary_data.append({
                    "月份": month_key,
                    "电商逾期订单数量": monthly_stats[month_key]["电商逾期订单数量"],
                    "租赁逾期订单数量": monthly_stats[month_key]["租赁逾期订单数量"]
                })
            
            total_time = time.time() - start_time
            logger.info(f"逾期订单汇总数据处理完成（高性能版本），共{len(summary_data)}个月份，耗时: {total_time:.4f}秒")
            return summary_data
        
        except Exception as e:
            logger.error(f"获取逾期订单汇总数据出错: {str(e)}")
            logger.exception("详细错误信息")
            raise
        finally:
            close_db_session(session)

    @classmethod
    def get_summary_data(cls, start_date, end_date, is_cumulative=False):
        """
        根据指定时间段统计店铺的数据汇总
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            is_cumulative: 是否为累计数据，影响日志输出内容
            
        Returns:
            tuple: (headers, summary_data) 汇总数据的表头和内容
        """
        # 记录函数开始执行时间
        start_time = time.time()
        query_type = "累计" if is_cumulative else "周期"
        logger.info(f"开始执行{query_type}汇总数据查询: {start_date} 至 {end_date}")
        
        session = get_db_session()
        try:
            # 记录数据库查询开始时间
            db_query_start = time.time()
            
            # 1. 计算订单数量和待收金额
            query_type = "累计" if is_cumulative else "常规"
            logger.info(f"开始计算{query_type}订单数据，时间范围: {start_date} 至 {end_date}")
            
            # 优化查询：预先加载关联表以减少后续查询
            from sqlalchemy.orm import selectinload
            
            # 使用单次查询预加载多个关联表，避免N+1查询问题
            orders = session.query(Order).options(
                selectinload(Order.payment_schedules),   # 预加载还款计划
                selectinload(Order.customer_info),       # 预加载客户信息
                selectinload(Order.transactions)         # 预加载交易记录
            ).filter(
                Order.order_date.between(start_date, end_date)
            ).all()
            
            # 记录订单查询耗时
            order_query_time = time.time() - db_query_start
            logger.info(f"{query_type}汇总-订单数据查询完成，耗时: {order_query_time:.4f}秒")
            
            # 数据处理开始时间
            process_start = time.time()
            
            # 2. 处理查询结果，填充店铺数据映射
            # 定义表头
            headers = [
                "店铺",
                "总台数",  # 更新表头名称，从"总订单数"改为"总台数"
                "总待收",
                "租赁待收",
                "电商待收",
                "增值费",
                "延保服务",
                "首付款",
                "租金",
                "尾款",
                "放款",
                "复投",
                "供应商利润",
                "成本",
                "电商业绩",
                "租赁业绩",
                "实际出资",
                "逾期本金",  # 新增逾期本金字段
                "逾期总待收",  # 新增逾期总待收字段
                "已完成订单",  # 新增：已完成订单数量
                "电商订单数",   # 新增：电商订单数
                "租赁订单数",   # 新增：租赁订单数
                "逾期订单数",   # 新增：逾期订单数
            ]
            
            # 定义分平台名称
            shop_names = ["太太租物", "涛涛好物", "刚刚好物", "林林租物", "太太享物"]
            
            # 存储各分平台的数据
            platform_summary = {
                shop: {
                    "总台数": 0,  # 总台数
                    "总待收": 0.0,
                    "租赁待收": 0.0,
                    "电商待收": 0.0,
                    "增值费": 0.0,
                    "延保服务": 0.0,
                    "首付款": 0.0,
                    "租金": 0.0,
                    "尾款": 0.0,
                    "放款": 0.0,
                    "复投": 0.0,
                    "供应商利润": 0.0,
                    "成本": 0.0,
                    "电商业绩": 0.0,
                    "租赁业绩": 0.0,
                    "实际出资": 0.0,
                    "逾期本金": 0.0,  # 新增逾期本金字段
                    "逾期总待收": 0.0,  # 新增逾期总待收字段
                    "逾期订单数": 0,   # 逾期订单数
                    "已完成订单": 0,   # 已完成订单数
                    "电商订单数": 0,   # 电商订单数
                    "租赁订单数": 0,   # 租赁订单数
                }
                for shop in shop_names
            }
            
            # 1. 计算订单数量和待收金额
            query_type = "累计" if is_cumulative else "常规"
            logger.info(f"开始计算{query_type}订单数据，时间范围: {start_date} 至 {end_date}")
            
            # 调试: 检查业务类型分布
            business_types = {}
            for order in orders:
                bt = (order.business_type or "").strip()
                if bt not in business_types:
                    business_types[bt] = 0
                business_types[bt] += 1
            
            logger.info(f"业务类型分布: {business_types}")
            
            # 强制设置业务类型映射
            product_type_mapping = {
                "租赁": ["租赁", "租", "租借"],
                "电商": ["电商", "电子商务", "网购"]
            }
            
            # 初始化业绩数据 - 用于存储订单的总应收
            ecommerce_receivable = {shop: 0.0 for shop in shop_names}
            lease_receivable = {shop: 0.0 for shop in shop_names}
            
            # 初始化业绩计算用的总应收（包括所有订单状态）
            ecommerce_total_receivable = {shop: 0.0 for shop in shop_names}
            lease_total_receivable = {shop: 0.0 for shop in shop_names}
            
            # 初始化延保服务和增值费
            ecommerce_additional_fee = {shop: {"增值费": 0.0, "延保服务": 0.0} for shop in shop_names}
            lease_additional_fee = {shop: {"增值费": 0.0, "延保服务": 0.0} for shop in shop_names}
            
            # 初始化交易金额统计 - 用于计算实际待收
            shop_transactions = {
                shop: {
                    "首付款": 0.0,
                    "租金": 0.0,
                    "尾款": 0.0
                } for shop in shop_names
            }
            
            # 初始化逾期订单集合
            overdue_orders = set()
            
            # 定义还款成功的状态
            completed_payment_statuses = ["按时还款", "协商结清", "逾期还款", "提前还款"]
            
            # 存储订单ID到订单类型的映射，便于后续交易记录处理
            order_type_map = {}
            
            for order in orders:
                shop = order.shop_affiliation
                if not shop or shop not in platform_summary:
                    continue
                
                # 获取台数（如果字段不存在或为空，默认为1）
                devices_count = getattr(order, 'devices_count', 1) or 1
                
                # 增加台数
                platform_summary[shop]["总台数"] += devices_count
                
                # 首先根据产品类型对所有订单进行分类统计
                product_type = (order.product_type or "").lower().strip()
                
                # 使用映射表判断产品类型
                is_ecommerce = any(keyword in product_type or product_type in keyword 
                                   for keyword in product_type_mapping["电商"])
                
                # 记录订单类型到映射表中，便于后续交易处理
                order_type_map[order.id] = "电商" if is_ecommerce else "租赁"
                
                # 所有订单都要计入电商或租赁订单数，不仅仅是"在途"的订单
                if is_ecommerce:
                    platform_summary[shop]["电商订单数"] += devices_count
                    
                    # 所有状态的订单都计入业绩金额
                    total_receivable = order.total_receivable or 0.0
                    ecommerce_total_receivable[shop] += total_receivable
                    
                    # 只有"在途"或"逾期"状态的订单才计入待收金额
                    if (order.status or "").strip() in ["在途", "逾期"]:
                        ecommerce_receivable[shop] += total_receivable
                else:
                    platform_summary[shop]["租赁订单数"] += devices_count
                    
                    # 所有状态的订单都计入业绩金额
                    total_receivable = order.total_receivable or 0.0
                    lease_total_receivable[shop] += total_receivable
                    
                    # 只有"在途"或"逾期"状态的订单才计入待收金额
                    if (order.status or "").strip() in ["在途", "逾期"]:
                        lease_receivable[shop] += total_receivable
                
                # 根据订单状态字段判断是否已完成或逾期
                order_status = (order.status or "").strip()
                
                # 基于status字段判断订单状态
                if order_status == "完结":
                    # 统计已完成订单台数
                    platform_summary[shop]["已完成订单"] += devices_count
                elif order_status == "逾期":
                    # 统计逾期订单台数
                    platform_summary[shop]["逾期订单数"] += devices_count
                    overdue_orders.add(order.id)
                    
                    # 累加逾期本金 - 从order.overdue_principal字段获取
                    overdue_principal = order.overdue_principal or 0.0
                    platform_summary[shop]["逾期本金"] += overdue_principal
                    
                    # 累加逾期总待收 - 从order.current_receivable字段获取
                    current_receivable = order.current_receivable or 0.0
                    platform_summary[shop]["逾期总待收"] += current_receivable
            
            # 加载一次性查询所有逾期订单信息，使用已知的逾期订单ID列表
            if overdue_orders:
                logger.info(f"查询逾期订单详情，共 {len(overdue_orders)} 个逾期订单")
            else:
                logger.info("没有发现逾期订单")
            
            # 记录数据处理耗时
            data_process_time = time.time() - process_start
            logger.info(f"{query_type}汇总-数据处理完成，耗时: {data_process_time:.4f}秒")
            
            # 数据格式化开始时间
            format_start = time.time()
            
            # 初始化总平台数据
            total_platform = {}
            
            # 3. 处理已分配的交易
            for transaction in session.query(Transaction).filter(
                Transaction.transaction_date.between(start_date, end_date)
            ).all():
                order = transaction.order
                if not order or not order.shop_affiliation or order.shop_affiliation not in platform_summary:
                    continue
                
                shop = order.shop_affiliation
                amount = transaction.amount or 0
                
                # 记录支付类交易金额，用于计算实际待收
                transaction_type = safe_str(transaction.transaction_type)
                
                # 记录首付款、租金、尾款交易，用于后续计算待收
                if any(keyword in transaction_type for keyword in ["首付", "首付款"]):
                    shop_transactions[shop]["首付款"] += amount
                elif any(keyword in transaction_type for keyword in ["租金"]):
                    shop_transactions[shop]["租金"] += amount
                elif any(keyword in transaction_type for keyword in ["尾款"]):
                    shop_transactions[shop]["尾款"] += amount
                
                # 根据订单ID查找订单类型
                order_id = transaction.order_id
                order_type = order_type_map.get(order_id)
                
                # 根据交易类型更新对应的数据
                matched = False
                for category, keywords in [
                    ("增值费", ["增值", "增值服务", "增值服务费"]),
                    ("延保服务", ["延保", "延保服务"]),
                    ("首付款", ["首付", "首付款"]),
                    ("租金", ["租金"]),
                    ("尾款", ["尾款"]),
                    ("放款", ["放款"]),
                    ("复投", ["复投"]),
                    ("供应商利润", ["供应商", "供应商利润"]),
                    ("成本", ["提成", "一次性支出", "固定支出", "风控充值", "服务器月租", "成本", "支出"])
                ]:
                    # 确保交易类型是字符串并去除空白
                    
                    # 调试输出
                    if category == "成本" and any(keyword in transaction_type for keyword in keywords):
                        logger.info(f"找到成本交易: {transaction_type}, 金额: {amount}, 关键词匹配: {[k for k in keywords if k in transaction_type]}")
                    
                    # 使用更直接的字符串匹配方式
                    if any(keyword in transaction_type for keyword in keywords):
                        if category in ["放款", "供应商利润", "成本"]:
                            # 这些是支出项，用负数表示
                            platform_summary[shop][category] -= amount
                            if category == "成本":
                                logger.info(f"累加成本: {shop} 增加 {-amount}, 当前总额: {platform_summary[shop][category]}")
                        else:
                            platform_summary[shop][category] += amount
                            
                            # 针对延保服务和增值费，记录到相应的业绩类型中
                            if category in ["延保服务", "增值费"]:
                                # 根据订单类型，计入相应的延保服务或增值费
                                if order_type == "电商":
                                    ecommerce_additional_fee[shop][category] += amount
                                    logger.info(f"添加电商'{category}': {shop} 增加 {amount}, 订单ID: {order_id}, 交易类型: {transaction_type}, 订单号: {order.order_number if order else 'Unknown'}")
                                elif order_type == "租赁":
                                    lease_additional_fee[shop][category] += amount
                                    logger.info(f"添加租赁'{category}': {shop} 增加 {amount}, 订单ID: {order_id}, 交易类型: {transaction_type}, 订单号: {order.order_number if order else 'Unknown'}")
                                else:
                                    # 如果找不到订单类型，根据订单的product_type判断
                                    if order and order.product_type:
                                        product_type = (order.product_type or "").lower().strip()
                                        is_ecommerce = any(keyword in product_type or product_type in keyword 
                                                        for keyword in product_type_mapping["电商"])
                                        
                                        if is_ecommerce:
                                            ecommerce_additional_fee[shop][category] += amount
                                            logger.info(f"添加电商'{category}'(通过product_type): {shop} 增加 {amount}, 订单product_type: {order.product_type}, 订单号: {order.order_number}")
                                        else:
                                            lease_additional_fee[shop][category] += amount
                                            logger.info(f"添加租赁'{category}'(通过product_type): {shop} 增加 {amount}, 订单product_type: {order.product_type}, 订单号: {order.order_number}")
                                    else:
                                        # 无法判断订单类型，平均分配给电商和租赁
                                        ecommerce_additional_fee[shop][category] += amount / 2
                                        lease_additional_fee[shop][category] += amount / 2
                                        logger.info(f"无法确定订单类型，'{category}'平均分配: {shop} 每类增加 {amount/2}")
                                    
                        matched = True
                        break
            
            # 记录数据格式化耗时
            format_time = time.time() - format_start
            logger.info(f"{query_type}汇总-数据格式化完成，耗时: {format_time:.4f}秒")
            
            # 查找没有店铺归属的成本交易
            unassigned_cost_transactions = session.query(Transaction).filter(
                Transaction.transaction_date.between(start_date, end_date),
                Transaction.transaction_type.in_(["提成", "一次性支出", "固定支出", "风控充值", "服务器月租", "成本", "支出"])
            ).all()
            
            # 统计未分配到店铺的成本
            unassigned_cost_value = 0.0
            for trans in unassigned_cost_transactions:
                # 检查该交易是否没有关联订单或没有店铺归属
                if not trans.order or not trans.order.shop_affiliation or trans.order.shop_affiliation not in shop_names:
                    amount = trans.amount or 0.0
                    unassigned_cost_value += amount
                    logger.info(f"发现无店铺归属的成本交易: ID={trans.id}, 类型={trans.transaction_type}, 金额={amount}, 日期={trans.transaction_date}")
            
            # 将未分配成本加入总平台成本
            # 获取各店铺已经分配的成本总额（排除总平台）
            assigned_platform_cost = sum(platform_summary[shop]["成本"] for shop in shop_names)
            
            # 先记录未分配成本到总平台
            total_platform["成本"] = assigned_platform_cost + abs(unassigned_cost_value)
            logger.info(f"总平台成本统计 - 已分配成本: {assigned_platform_cost}, 未分配成本绝对值: {abs(unassigned_cost_value)}, 总成本: {total_platform['成本']}")
            
            # 如果存在未分配成本，按比例分配给各个店铺（排除总平台）
            if abs(unassigned_cost_value) > 0.01:
                # 检查各店铺成本总和是否有意义（大于0）
                if abs(assigned_platform_cost) > 0.01:
                    # 按各店铺已有成本比例分配未分配成本
                    logger.info(f"开始按比例分配未分配成本: {abs(unassigned_cost_value)}, 基于各店铺已分配成本")
                    for shop in shop_names:
                        shop_ratio = platform_summary[shop]["成本"] / assigned_platform_cost
                        shop_portion = abs(unassigned_cost_value) * shop_ratio
                        # 将未分配成本加到各店铺
                        platform_summary[shop]["成本"] += shop_portion
                        logger.info(f"按比例将未分配成本 {shop_portion} 分配给店铺 {shop} (比例: {shop_ratio:.4f})")
                else:
                    # 如果所有店铺都没有已分配成本，则平均分配
                    equal_portion = abs(unassigned_cost_value) / len(shop_names)
                    logger.info(f"没有已分配成本，平均分配未分配成本: {equal_portion}/店铺")
                    for shop in shop_names:
                        platform_summary[shop]["成本"] += equal_portion
                        logger.info(f"平均将未分配成本 {equal_portion} 分配给店铺 {shop}")
            
            # 这里是修改的核心部分：针对非累计查询，重新计算待收金额
            if not is_cumulative:
                # 开始重新计算非累计查询的待收金额
                recalc_start_time = time.time()
                logger.info(f"周期查询 - 开始重新计算时间段内待收金额: {start_date} 至 {end_date}")
                
                # 1. 查询时间段内的所有交易记录
                period_transactions = session.query(Transaction).filter(
                    Transaction.transaction_date.between(start_date, end_date)
                ).all()
                
                logger.info(f"查询到时间段内共{len(period_transactions)}笔交易记录")
                
                # 2. 按订单ID、交易类型分组计算时间段内的交易
                period_repayments_by_order = defaultdict(float)
                period_ecommerce_by_shop = {shop: 0.0 for shop in shop_names}
                period_lease_by_shop = {shop: 0.0 for shop in shop_names}
                involved_order_ids = set()
                
                # 处理时间段内的交易记录
                for trans in period_transactions:
                    if trans.order_id and trans.order:
                        order_id = trans.order_id
                        involved_order_ids.add(order_id)
                        amount = trans.amount or 0
                        transaction_type = safe_str(trans.transaction_type).lower()
                        
                        # 获取订单所属店铺
                        order = trans.order
                        shop = order.shop_affiliation
                        if not shop or shop not in shop_names:
                            continue
                            
                        # 获取订单类型（电商/租赁）
                        product_type = (order.product_type or "").lower().strip()
                        is_ecommerce = any(keyword in product_type or product_type in keyword 
                                          for keyword in product_type_mapping["电商"])
                        
                        # 统计还款交易金额
                        if any(keyword in transaction_type for keyword in ["首付", "首付款", "租金", "尾款"]):
                            period_repayments_by_order[order_id] += amount
                            
                        # 根据订单类型，累加到电商或租赁金额
                        # 每个订单只会被累加一次总金额，因此需要判断订单是否已被处理
                        if is_ecommerce and order.order_date and start_date <= order.order_date <= end_date:
                            period_ecommerce_by_shop[shop] += order.total_receivable or 0
                        elif not is_ecommerce and order.order_date and start_date <= order.order_date <= end_date:
                            period_lease_by_shop[shop] += order.total_receivable or 0
                
                logger.info(f"时间段内交易涉及{len(involved_order_ids)}个订单")
                
                # 3. 查询所有订单
                all_orders = session.query(Order).all()
                
                logger.info(f"查询到总共{len(all_orders)}个订单")
                
                # 4. 按店铺重新计算待收金额
                for shop in shop_names:
                    # 初始化该店铺的待收金额计数器
                    lease_period_receivable = 0.0
                    ecommerce_period_receivable = 0.0
                    
                    # 更新"租赁"和"电商"字段
                    platform_summary[shop]["租赁待收"] = period_lease_by_shop[shop]
                    platform_summary[shop]["电商待收"] = period_ecommerce_by_shop[shop]
                    
                    # 过滤该店铺的订单
                    shop_orders = [order for order in all_orders if order.shop_affiliation == shop]
                    logger.info(f"店铺 {shop} 共有{len(shop_orders)}个订单")
                    
                    # 计算每个订单在该时间段的实际待收
                    for order in shop_orders:
                        # 跳过已完成的订单
                        if (order.status or "").strip() == "完结":
                            continue
                            
                        # 获取订单类型（电商/租赁）
                        product_type = (order.product_type or "").lower().strip()
                        is_ecommerce = any(keyword in product_type or product_type in keyword 
                                          for keyword in product_type_mapping["电商"])
                        
                        # 只需要考虑在时间段范围内的订单
                        if (order.order_date and start_date <= order.order_date <= end_date):
                            # 获取订单的总应收金额
                            order_total = order.total_receivable or 0.0
                            
                            # 获取订单在当前时间段的还款金额
                            period_repaid = period_repayments_by_order.get(order.id, 0.0)
                            
                            # 只有在途和逾期状态的订单才计算待收
                            if (order.status or "").strip() in ["在途", "逾期"]:
                                # 计算当前时间段实际待收 = 总应收 - 时间段内的还款
                                period_receivable = max(0, order_total - period_repaid)
                                
                                # 根据订单类型累加待收
                                if is_ecommerce:
                                    ecommerce_period_receivable += period_receivable
                                else:
                                    lease_period_receivable += period_receivable
                        
                        # 更新平台汇总数据
                        platform_summary[shop]["租赁待收"] = lease_period_receivable
                        platform_summary[shop]["电商待收"] = ecommerce_period_receivable
                        platform_summary[shop]["总待收"] = lease_period_receivable + ecommerce_period_receivable
                        
                        # 记录重新计算的结果
                        logger.info(f"周期查询 - 店铺 {shop} 重新计算待收结果:")
                        logger.info(f"  - 租赁待收: {lease_period_receivable}")
                        logger.info(f"  - 电商待收: {ecommerce_period_receivable}")
                        logger.info(f"  - 总待收: {lease_period_receivable + ecommerce_period_receivable}")
                        logger.info(f"  - 租赁总额: {platform_summary[shop]['租赁待收']}")
                        logger.info(f"  - 电商总额: {platform_summary[shop]['电商待收']}")
                    
                    recalc_time = time.time() - recalc_start_time
                    logger.info(f"周期查询 - 重新计算待收金额完成，耗时: {recalc_time:.4f}秒")
            else:
                # 对于累计查询，需要重新计算正确的待收金额
                cumul_start_time = time.time()
                logger.info("累计查询 - 开始计算待收金额和其他数据")
                
                # 初始化累计待收金额
                cumulative_lease_receivable = {shop: 0.0 for shop in shop_names}
                cumulative_ecommerce_receivable = {shop: 0.0 for shop in shop_names}
                cumulative_total_receivable = {shop: 0.0 for shop in shop_names}
                
                # 初始化租赁和电商总额
                cumulative_lease_total = {shop: 0.0 for shop in shop_names}
                cumulative_ecommerce_total = {shop: 0.0 for shop in shop_names}
                
                # 查询所有订单（不限状态，但限定截至日期，用于计算"租赁"和"电商"总额）
                all_orders = session.query(Order).filter(
                    Order.order_date <= end_date  # 限定截至日期
                ).all()
                logger.info(f"累计查询 - 找到{len(all_orders)}个订单用于统计租赁和电商总额（截至{end_date}）")
                
                # 计算租赁和电商总额
                for order in all_orders:
                    shop = order.shop_affiliation
                    if not shop or shop not in shop_names:
                        continue
                    
                    # 获取订单类型
                    product_type = (order.product_type or "").lower().strip()
                    is_ecommerce = any(keyword in product_type or product_type in keyword 
                                      for keyword in product_type_mapping["电商"])
                    
                    # 获取订单总金额
                    total_receivable = order.total_receivable or 0.0
                    
                    # 按产品类型累加到对应的总额
                    if is_ecommerce:
                        cumulative_ecommerce_total[shop] += total_receivable
                    else:
                        cumulative_lease_total[shop] += total_receivable
                
                # 查询所有在途和逾期状态的订单（限定截至日期，用于计算待收）
                active_orders = session.query(Order).filter(
                    Order.status.in_(["在途", "逾期"]),
                    Order.order_date <= end_date  # 限定截至日期
                ).all()
                
                logger.info(f"累计查询 - 找到{len(active_orders)}个在途和逾期订单用于计算待收（截至{end_date}）")
                
                # 计算每个店铺的累计待收金额
                for order in active_orders:
                    shop = order.shop_affiliation
                    if not shop or shop not in shop_names:
                        continue
                    
                    # 获取订单类型
                    product_type = (order.product_type or "").lower().strip()
                    is_ecommerce = any(keyword in product_type or product_type in keyword 
                                      for keyword in product_type_mapping["电商"])
                    
                    # 获取订单当前待收金额
                    current_receivable = order.current_receivable or 0.0
                    
                    # 根据订单类型累加待收
                    if current_receivable > 0:
                        if is_ecommerce:
                            cumulative_ecommerce_receivable[shop] += current_receivable
                        else:
                            cumulative_lease_receivable[shop] += current_receivable
                        
                        # 累加总待收
                        cumulative_total_receivable[shop] += current_receivable
                
                # 查询所有成本相关交易（限定截至日期）
                cost_transactions = session.query(Transaction).filter(
                    Transaction.transaction_date <= end_date,  # 限定截至日期
                    Transaction.transaction_type.in_(["提成", "一次性支出", "固定支出", "风控充值", "服务器月租", "成本", "支出"])
                ).all()
                
                logger.info(f"累计查询 - 找到{len(cost_transactions)}笔成本相关交易（截至{end_date}）")
                
                # 计算每个店铺的成本支出
                cumulative_cost = {shop: 0.0 for shop in shop_names}
                unassigned_cost = 0.0  # 用于记录未分配到特定店铺的成本
                
                for trans in cost_transactions:
                    amount = trans.amount or 0.0
                    
                    # 检查该交易是否有关联订单和店铺归属
                    if not trans.order or not trans.order.shop_affiliation or trans.order.shop_affiliation not in shop_names:
                        # 无店铺归属的成本，记录下来
                        unassigned_cost += amount
                        logger.info(f"发现无店铺归属的成本交易: ID={trans.id}, 类型={trans.transaction_type}, 金额={amount}, 日期={trans.transaction_date}")
                        continue
                        
                    shop = trans.order.shop_affiliation
                    # 累加成本（成本为支出，使用负值）
                    cumulative_cost[shop] -= amount
                    logger.info(f"累加成本到店铺 {shop}: 交易ID={trans.id}, 类型={trans.transaction_type}, 金额={amount}, 日期={trans.transaction_date}")
                
                logger.info(f"未分配到店铺的成本总额: {unassigned_cost}")
                
                # 计算总成本（包括未分配成本）
                total_assigned_cost = sum(cumulative_cost.values())
                total_cost = total_assigned_cost + unassigned_cost
                logger.info(f"总成本: {total_cost} (已分配: {total_assigned_cost}, 未分配: {unassigned_cost})")
                
                # 如果存在未分配成本，按比例分配到各店铺
                if unassigned_cost > 0:
                    # 根据已有成本比例分配未分配成本
                    # 如果没有已分配成本，则平均分配
                    if abs(total_assigned_cost) > 0.01:
                        for shop in shop_names:
                            shop_ratio = abs(cumulative_cost[shop]) / abs(total_assigned_cost) if abs(total_assigned_cost) > 0 else 1.0 / len(shop_names)
                            shop_portion = unassigned_cost * shop_ratio
                            cumulative_cost[shop] -= shop_portion  # 减去因为是用负值表示成本
                            logger.info(f"按比例将未分配成本 {shop_portion} 分配给店铺 {shop} (比例: {shop_ratio:.4f})")
                    else:
                        # 如果没有已分配成本，则平均分配
                        equal_portion = unassigned_cost / len(shop_names)
                        for shop in shop_names:
                            cumulative_cost[shop] -= equal_portion
                            logger.info(f"平均将未分配成本 {equal_portion} 分配给店铺 {shop}")
                
                # 更新各店铺的数据
                for shop in shop_names:
                    # 更新待收金额
                    platform_summary[shop]["租赁待收"] = cumulative_lease_receivable[shop]
                    platform_summary[shop]["电商待收"] = cumulative_ecommerce_receivable[shop]
                    platform_summary[shop]["总待收"] = cumulative_total_receivable[shop]
                    
                    # 更新租赁和电商总额
                    platform_summary[shop]["租赁"] = cumulative_lease_total[shop]
                    platform_summary[shop]["电商"] = cumulative_ecommerce_total[shop]
                    
                    # 更新成本 - 确保是正数，因为成本是支出项
                    platform_summary[shop]["成本"] = abs(cumulative_cost[shop])
                    
                    # 更新逾期相关字段 - 只处理逾期状态的订单
                    if (order.status or "").strip() == "逾期":
                        # 累加逾期本金 - 从order.overdue_principal字段获取
                        overdue_principal = order.overdue_principal or 0.0
                        platform_summary[shop]["逾期本金"] += overdue_principal
                        
                        # 累加逾期总待收 - 从order.current_receivable字段获取
                        current_receivable = order.current_receivable or 0.0
                        platform_summary[shop]["逾期总待收"] += current_receivable
                
                cumul_time = time.time() - cumul_start_time
                logger.info(f"累计查询 - 计算完成，耗时: {cumul_time:.4f}秒")
                        
            # 根据收集的交易数据，计算业绩和实际出资
            logger.info("开始计算业绩和实际出资")
            
            # 打印各店铺的增值费和延保服务统计
            for shop in shop_names:
                logger.info(f"店铺 {shop} 增值费和延保服务统计:")
                logger.info(f"  - 电商增值费: {ecommerce_additional_fee[shop]['增值费']}")
                logger.info(f"  - 电商延保服务: {ecommerce_additional_fee[shop]['延保服务']}")
                logger.info(f"  - 租赁增值费: {lease_additional_fee[shop]['增值费']}")
                logger.info(f"  - 租赁延保服务: {lease_additional_fee[shop]['延保服务']}")
                logger.info(f"  - 总增值费: {platform_summary[shop]['增值费']}")
                logger.info(f"  - 总延保服务: {platform_summary[shop]['延保服务']}")
                
            for shop in shop_names:
                shop_data = platform_summary[shop]
                
                # 确保相关字段有值，防止数据缺失
                if "租赁待收" not in shop_data:
                    shop_data["租赁待收"] = 0.0
                if "电商待收" not in shop_data:
                    shop_data["电商待收"] = 0.0
                
                # 电商业绩和租赁业绩直接从订单的应收账单获取
                # 电商业绩 = 电商订单的total_receivable
                shop_data["电商业绩"] = ecommerce_total_receivable[shop]
                
                # 租赁业绩 = 租赁订单的total_receivable
                shop_data["租赁业绩"] = lease_total_receivable[shop]
                
                # 调试输出
                logger.info(f"店铺 {shop} 电商业绩计算:")
                logger.info(f"  - 电商总额 (直接作为电商业绩): {ecommerce_total_receivable[shop]}")
                logger.info(f"  - 电商待收: {shop_data['电商待收']}")
                logger.info(f"  - 电商业绩总计: {shop_data['电商业绩']}")
                
                logger.info(f"店铺 {shop} 租赁业绩计算:")
                logger.info(f"  - 租赁总额 (直接作为租赁业绩): {lease_total_receivable[shop]}")
                logger.info(f"  - 租赁待收: {shop_data['租赁待收']}")
                logger.info(f"  - 租赁业绩总计: {shop_data['租赁业绩']}")
                
                # 实际出资计算保持不变
                shop_data["实际出资"] = (
                    abs(shop_data["放款"]) + 
                    abs(shop_data["供应商利润"]) + 
                    abs(shop_data["成本"])
                ) - (
                    shop_data["增值费"] + 
                    shop_data["延保服务"] + 
                    shop_data["首付款"] + 
                    shop_data["租金"] + 
                    shop_data["尾款"]
                )
            
            # 根据收集的交易数据，计算总平台数据
            logger.info("开始计算总平台数据")
            total_platform = {
                field: sum(shop_data[field] for shop_data in platform_summary.values())
                for field in platform_summary["太太租物"].keys()
            }
            
            # 记录总业绩
            logger.info(f"总平台 电商业绩: {total_platform['电商业绩']}")
            logger.info(f"总平台 租赁业绩: {total_platform['租赁业绩']}")
            
            # 记录未分配成本的变量，用于调试和日志
            unassigned_cost_value = 0.0
            
            # 查询所有未关联订单或未归属店铺的成本交易
            # 累计查询模式下的未分配成本查询
            if is_cumulative:
                unassigned_cost_transactions = session.query(Transaction).filter(
                    Transaction.transaction_date <= end_date,  # 修改：限制截至日期
                    Transaction.transaction_type.in_(["提成", "一次性支出", "固定支出", "风控充值", "服务器月租", "成本", "支出"])
                ).all()
                
                # 统计未分配到店铺的成本
                for trans in unassigned_cost_transactions:
                    if not trans.order or not trans.order.shop_affiliation or trans.order.shop_affiliation not in shop_names:
                        amount = trans.amount or 0.0
                        unassigned_cost_value += amount
                        logger.info(f"总平台成本统计 - 发现无店铺归属的成本交易: ID={trans.id}, 类型={trans.transaction_type}, 金额={amount}, 日期={trans.transaction_date}")
                
                # 查询所有订单中未分配到店铺的成本
                all_orders_without_shop = session.query(Order).filter(
                    Order.order_date <= end_date,  # 修改：限制截至日期
                    or_(
                        Order.shop_affiliation.is_(None),
                        ~Order.shop_affiliation.in_(shop_names)
                    )
                ).all()
                
                for order in all_orders_without_shop:
                    if order.cost:
                        cost = abs(order.cost)
                        unassigned_cost_value += cost
                        logger.info(f"总平台成本统计 - 发现无店铺归属的订单成本: 订单ID={order.id}, 成本={cost}")
            else:
                # 周期查询模式下的未分配成本查询 
                unassigned_cost_transactions = session.query(Transaction).filter(
                    Transaction.transaction_date.between(start_date, end_date),
                    Transaction.transaction_type.in_(["提成", "一次性支出", "固定支出", "风控充值", "服务器月租", "成本", "支出"])
                ).all()
                
                # 统计未分配到店铺的成本
                for trans in unassigned_cost_transactions:
                    if not trans.order or not trans.order.shop_affiliation or trans.order.shop_affiliation not in shop_names:
                        amount = trans.amount or 0.0
                        unassigned_cost_value += amount
                        logger.info(f"总平台成本统计 - 发现无店铺归属的成本交易: ID={trans.id}, 类型={trans.transaction_type}, 金额={amount}, 日期={trans.transaction_date}")
                
                # 查询时间范围内所有订单中未分配到店铺的成本
                all_orders_without_shop = session.query(Order).filter(
                    Order.order_date.between(start_date, end_date),
                    or_(
                        Order.shop_affiliation.is_(None),
                        ~Order.shop_affiliation.in_(shop_names)
                    )
                ).all()
                
                for order in all_orders_without_shop:
                    if order.cost:
                        cost = abs(order.cost)
                        unassigned_cost_value += cost
                        logger.info(f"总平台成本统计 - 发现无店铺归属的订单成本: 订单ID={order.id}, 成本={cost}")
            
            # 将未分配成本加入总平台成本
            # 获取各店铺已经分配的成本总额（排除总平台）
            assigned_platform_cost = sum(platform_summary[shop]["成本"] for shop in shop_names)
            
            # 先记录未分配成本到总平台
            total_platform["成本"] = assigned_platform_cost + abs(unassigned_cost_value)
            logger.info(f"总平台成本统计 - 已分配成本: {assigned_platform_cost}, 未分配成本绝对值: {abs(unassigned_cost_value)}, 总成本: {total_platform['成本']}")
            
            # 如果存在未分配成本，按比例分配给各个店铺（排除总平台）
            if abs(unassigned_cost_value) > 0.01:
                # 检查各店铺成本总和是否有意义（大于0）
                if abs(assigned_platform_cost) > 0.01:
                    # 按各店铺已有成本比例分配未分配成本
                    logger.info(f"开始按比例分配未分配成本: {abs(unassigned_cost_value)}, 基于各店铺已分配成本")
                    for shop in shop_names:
                        shop_ratio = platform_summary[shop]["成本"] / assigned_platform_cost
                        shop_portion = abs(unassigned_cost_value) * shop_ratio
                        # 将未分配成本加到各店铺
                        platform_summary[shop]["成本"] += shop_portion
                        logger.info(f"按比例将未分配成本 {shop_portion} 分配给店铺 {shop} (比例: {shop_ratio:.4f})")
                else:
                    # 如果所有店铺都没有已分配成本，则平均分配
                    equal_portion = abs(unassigned_cost_value) / len(shop_names)
                    logger.info(f"没有已分配成本，平均分配未分配成本: {equal_portion}/店铺")
                    for shop in shop_names:
                        platform_summary[shop]["成本"] += equal_portion
                        logger.info(f"平均将未分配成本 {equal_portion} 分配给店铺 {shop}")
            
            # 更新总平台的实际出资计算
            total_platform["实际出资"] = (
                abs(total_platform["放款"]) + 
                abs(total_platform["供应商利润"]) + 
                abs(total_platform["成本"])
            ) - (
                total_platform["增值费"] + 
                total_platform["延保服务"] + 
                total_platform["首付款"] + 
                total_platform["租金"] + 
                total_platform["尾款"]
            )
            
            # 5. 构建汇总数据
            summary_data = []
            
            # 添加总平台数据
            summary_data.append([
                "总平台",
                total_platform["总台数"],
                round(total_platform["总待收"], 2),
                round(total_platform["租赁待收"], 2),
                round(total_platform["电商待收"], 2),
                round(total_platform["增值费"], 2),
                round(total_platform["延保服务"], 2),
                round(total_platform["首付款"], 2),
                round(total_platform["租金"], 2),
                round(total_platform["尾款"], 2),
                round(abs(total_platform["放款"]), 2),
                round(total_platform["复投"], 2),
                round(abs(total_platform["供应商利润"]), 2),
                round(abs(total_platform["成本"]), 2),
                round(total_platform["电商业绩"], 2),
                round(total_platform["租赁业绩"], 2),
                round(total_platform["实际出资"], 2),
                round(total_platform["逾期本金"], 2),
                round(total_platform["逾期总待收"], 2),
                total_platform["已完成订单"],  # 新增：已完成订单数量
                total_platform["电商订单数"],   # 新增：电商订单数
                total_platform["租赁订单数"],   # 新增：租赁订单数
                total_platform["逾期订单数"],   # 新增：逾期订单数
            ])
            
            # 添加各分平台数据
            for shop in shop_names:
                shop_data = platform_summary[shop]
                summary_data.append([
                    shop,
                    shop_data["总台数"],
                    round(shop_data["总待收"], 2),
                    round(shop_data["租赁待收"], 2),
                    round(shop_data["电商待收"], 2),
                    round(shop_data["增值费"], 2),
                    round(shop_data["延保服务"], 2),
                    round(shop_data["首付款"], 2),
                    round(shop_data["租金"], 2),
                    round(shop_data["尾款"], 2),
                    round(abs(shop_data["放款"]), 2),
                    round(shop_data["复投"], 2),
                    round(abs(shop_data["供应商利润"]), 2),
                    round(abs(shop_data["成本"]), 2),
                    round(shop_data["电商业绩"], 2),
                    round(shop_data["租赁业绩"], 2),
                    round(shop_data["实际出资"], 2),
                    round(shop_data["逾期本金"], 2),
                    round(shop_data["逾期总待收"], 2),
                    shop_data["已完成订单"],  # 新增：已完成订单数量
                    shop_data["电商订单数"],   # 新增：电商订单数
                    shop_data["租赁订单数"],   # 新增：租赁订单数
                    shop_data["逾期订单数"],   # 新增：逾期订单数
                ])
            
            # 重要修复：如果这不是累计查询调用，我们不应该添加累计数据
            # 累计数据应该由累计查询（is_cumulative=True）专门生成
            if not is_cumulative:
                logger.info("周期查询不生成累计数据")
                return headers, summary_data
                
            # 如果是累计查询，则返回累计数据（不再添加"累计 "前缀，而是直接返回数据）
            # 累计查询的数据已经是累计结果，不需要重复计算
            logger.info(f"{query_type}数据汇总成功，时间范围: {start_date} 至 {end_date}")
            return headers, summary_data
        
        except Exception as e:
            logger.error(f'获取{query_type}汇总数据出错: {str(e)}')
            logger.exception("详细错误信息")
            raise
        finally:
            close_db_session(session)
            # 记录总执行时间
            total_time = time.time() - start_time
            logger.info(f"{query_type}汇总数据查询完成，总耗时: {total_time:.4f}秒")

    @classmethod
    def get_overdue_data_by_ids(cls, order_ids, session=None):
        """
        根据订单ID列表获取逾期金额数据
        
        Args:
            order_ids: 订单ID列表
            session: 可选的数据库会话
            
        Returns:
            dict: 订单ID到逾期金额的映射
        """
        # 记录函数开始执行时间
        start_time = time.time()
        logger.info(f"开始查询逾期数据，订单数量: {len(order_ids)}")
        
        close_session = False
        if session is None:
            session = get_db_session()
            close_session = True
            
        try:
            # 查询这些订单的所有逾期未还账单
            overdue_schedules = session.query(
                PaymentSchedule
            ).filter(
                PaymentSchedule.order_id.in_(order_ids),
                PaymentSchedule.status.like("%逾期未还%")
            ).all()
            
            # 计算每个订单的逾期金额
            overdue_amounts = {}
            for schedule in overdue_schedules:
                order_id = schedule.order_id
                if order_id not in overdue_amounts:
                    overdue_amounts[order_id] = 0.0
                    
                # 逾期金额 = 账单金额 - 已还金额
                amount = schedule.amount or 0  # 确保 amount 不是 None
                paid_amount = schedule.paid_amount or 0  # 确保 paid_amount 不是 None
                overdue_amount = amount - paid_amount
                if overdue_amount > 0:
                    overdue_amounts[order_id] += overdue_amount
                    
            return overdue_amounts
        finally:
            if close_session:
                close_db_session(session)
            # 记录总执行时间
            total_time = time.time() - start_time
            logger.info(f"逾期数据查询完成，包含{len(overdue_amounts) if 'overdue_amounts' in locals() else 0}条记录，总耗时: {total_time:.4f}秒")

    @classmethod
    def filter_orders_by_date(cls, filter_date):
        """
        根据账单日期筛选待还订单（状态为"在途"或"逾期"的订单）
        
        Args:
            filter_date: 账单日期
            
        Returns:
            list: 符合条件的订单列表
        """
        start_time = time.time()
        logger.info(f"开始执行日期筛选查询，查询账单日期: {filter_date}")
        
        session = None
        try:
            session = get_db_session()
            # 查询具有指定账单日期且状态为"在途"或"逾期"的订单
            # 首先查找指定日期的账单记录
            payment_schedules = session.query(PaymentSchedule).filter(
                PaymentSchedule.due_date == filter_date
            ).all()
            
            # 获取对应的订单ID
            order_ids = [ps.order_id for ps in payment_schedules]
            
            # 查询这些订单中状态为"在途"或"逾期"的订单
            orders = []
            if order_ids:
                orders = session.query(Order).filter(
                    Order.id.in_(order_ids),
                    Order.status.in_(["在途", "逾期"])  # 确保只包含"在途"和"逾期"状态
                ).all()
            
            # 转换为可序列化的结果
            results = []
            for order in orders:
                # 获取客户信息
                customer_info = {}
                if order.customer_info:
                    customer_info = {
                        '手机号': order.customer_info.phone,
                        '客服': order.customer_info.customer_service,
                        '业务归属': order.customer_info.business_affiliation,
                        '备注': order.customer_info.remarks
                    }
                
                # 查找当前期数信息和账单状态
                current_period = 0
                bill_status = ""
                current_bill_amount = 0.0
                for ps in order.payment_schedules:
                    if ps.due_date and ps.due_date == filter_date:
                        current_period = ps.period_number
                        bill_status = ps.status or "未知"
                        # 计算当前账单待收金额 = 应付金额 - 已付金额
                        bill_amount = ps.amount or 0.0
                        paid_amount = ps.paid_amount or 0.0
                        current_bill_amount = bill_amount - paid_amount
                        if current_bill_amount < 0:
                            current_bill_amount = 0.0
                        break
                
                # 构建订单数据，按类别规划结构
                order_data = {
                    # 订单基本信息
                    '订单信息': {
                        '订单ID': order.id,
                        '订单编号': order.order_number,
                        '订单日期': order.order_date.strftime("%Y-%m-%d") if order.order_date else "",
                        '客户姓名': order.customer_name,
                        '设备型号': order.model,
                        '当前待收': float(order.current_receivable) if order.current_receivable else 0.0,
                        '总期数': order.periods,
                        '台数': order.devices_count or 1,
                        '备注': order.remarks
                    },
                    # 客户信息
                    '客户信息': customer_info,
                    # 账单信息
                    '账单信息': {
                        '账单日期': filter_date.strftime("%Y-%m-%d"),
                        '当前期数': current_period,
                        '账单状态': bill_status,
                        '当期待收': round(current_bill_amount, 2)
                    }
                }
                results.append(order_data)
            
            # 记录执行时间
            total_time = time.time() - start_time
            logger.info(f"日期筛选查询完成，找到{len(results)}条记录，耗时: {total_time:.4f}秒")
            
            return results
        except Exception as e:
            logger.error(f"日期筛选查询异常: {e}")
            if session:
                session.rollback()
            raise
        finally:
            if session:
                close_db_session(session)

    @classmethod
    def filter_orders_by_customer_name(cls, customer_name):
        """
        根据客户姓名筛选订单，并返回订单信息，包括账单日期和状态
        
        Args:
            customer_name: 客户姓名查询字符串
            
        Returns:
            list: 符合条件的订单列表，带账单日期和状态信息
        """
        start_time = time.time()
        logger.info(f"开始执行客户姓名筛选查询，查询参数: {customer_name}")
        
        session = get_db_session()
        try:
            # 名称模糊查询
            orders = session.query(Order).filter(
                Order.customer_name.like(f"%{customer_name}%")
            ).order_by(Order.order_date.desc()).all()
            
            # 转换为可序列化的结果
            results = []
            for order in orders:
                # 获取还款计划信息
                bill_dates_statuses = [''] * 6  # 初始化6个空字符串
                
                for i, ps in enumerate(sorted(order.payment_schedules, key=lambda ps: ps.period_number or 0)[:6]):
                    due_date_str = ps.due_date.strftime("%Y-%m-%d") if ps.due_date else ''
                    status = ps.status or '未知'
                    
                    if due_date_str:
                        bill_info = f"{due_date_str}（{status}）"
                    else:
                        bill_info = f"空白日期（{status}）"
                    bill_dates_statuses[i] = bill_info
                
                # 构建订单数据
                order_date_str = order.order_date.strftime("%Y-%m-%d") if order.order_date else ''
                
                # 基本订单信息
                order_data = {
                    '订单日期': order_date_str,
                    '订单编号': order.order_number or '',
                    '客户姓名': order.customer_name or '',
                    '产品': order.product_type or '',
                    '期数': str(order.periods) if order.periods else '',
                    '总待收': "{:.2f}".format(order.total_receivable or 0),
                    '当前待收': "{:.2f}".format(order.current_receivable or 0),
                    '期数1': bill_dates_statuses[0],
                    '期数2': bill_dates_statuses[1],
                    '期数3': bill_dates_statuses[2],
                    '期数4': bill_dates_statuses[3],
                    '期数5': bill_dates_statuses[4],
                    '期数6': bill_dates_statuses[5]
                }
                
                # 添加客户信息
                if order.customer_info:
                    additional_info = {
                        '手机号码': order.customer_info.phone or '',
                        '客服': order.customer_info.customer_service or '',
                        '业务': order.customer_info.business_affiliation or '',
                        '客户信息备注': order.customer_info.remarks or ''
                    }
                    order_data.update(additional_info)
                
                # 添加台数和成本信息
                order_data['台数'] = order.devices_count or 1
                order_data['成本'] = "{:.2f}".format(order.cost or 0)
                
                # 添加备注信息
                order_data['备注'] = order.remarks or ''
                
                results.append(order_data)
            
            # 记录执行时间
            total_time = time.time() - start_time
            logger.info(f"客户姓名筛选查询完成，找到{len(results)}条记录，耗时: {total_time:.4f}秒")
            
            return results
        except Exception as e:
            logger.error(f"客户姓名筛选查询异常: {e}")
            raise
        finally:
            close_db_session(session)

    @classmethod
    def filter_overdue_orders(cls):
        """
        筛选逾期订单（首次逾期）- 优化版本，使用SQL聚合查询
        
        Returns:
            list: 符合条件的逾期订单列表
        """
        start_time = time.time()
        logger.info("开始执行逾期订单筛选查询（优化版本）")
        
        # 获取当前日期，用于计算逾期天数
        today = date.today()
        
        session = get_db_session()
        try:
            # 使用原生SQL进行优化查询，避免N+1问题
            sql = text("""
                SELECT DISTINCT
                    o.id, o.order_number, o.order_date, o.customer_name, 
                    o.product_type, o.model, o.periods, 
                    COALESCE(o.devices_count, 1) as devices_count,
                    COALESCE(o.total_receivable, 0) as total_receivable, 
                    COALESCE(o.current_receivable, 0) as current_receivable, 
                    COALESCE(o.cost, 0) as cost, 
                    COALESCE(o.remarks, '') as remarks,
                    COALESCE(ci.phone, '') as phone, 
                    COALESCE(ci.customer_service, '') as customer_service, 
                    COALESCE(ci.business_affiliation, '') as business_affiliation, 
                    COALESCE(ci.remarks, '') as customer_remarks,
                    -- 使用聚合函数计算逾期金额
                    COALESCE(overdue_agg.overdue_amount, 0) as overdue_amount,
                    overdue_agg.overdue_periods_count,
                    -- 获取首次逾期信息
                    overdue_agg.first_overdue_date,
                    overdue_agg.first_overdue_period,
                    overdue_agg.first_overdue_days
                FROM orders o
                LEFT JOIN customer_info ci ON o.id = ci.order_id  
                LEFT JOIN (
                    SELECT 
                        ps.order_id,
                        -- 修复逾期金额计算：严格匹配原始逻辑，只有正数才累加
                        SUM(CASE 
                            WHEN ps.status IS NOT NULL 
                            AND ps.status LIKE '%逾期%'
                            AND COALESCE(ps.amount, 0) > COALESCE(ps.paid_amount, 0)
                            THEN COALESCE(ps.amount, 0) - COALESCE(ps.paid_amount, 0)
                            ELSE 0 
                        END) as overdue_amount,
                        COUNT(CASE 
                            WHEN ps.status IS NOT NULL 
                            AND ps.status LIKE '%逾期%' 
                            THEN 1 
                        END) as overdue_periods_count,
                        MIN(CASE WHEN ps.status LIKE '%逾期%' THEN ps.due_date END) as first_overdue_date,
                        MIN(CASE WHEN ps.status LIKE '%逾期%' THEN ps.period_number END) as first_overdue_period,
                        -- 计算首次逾期天数（PostgreSQL语法）
                        CASE 
                            WHEN MIN(CASE WHEN ps.status LIKE '%逾期%' THEN ps.due_date END) IS NOT NULL 
                            THEN GREATEST((CURRENT_DATE - MIN(CASE WHEN ps.status LIKE '%逾期%' THEN ps.due_date END))::INTEGER, 0)
                            ELSE 0 
                        END as first_overdue_days
                    FROM payment_schedules ps 
                    WHERE ps.status IS NOT NULL AND ps.status LIKE '%逾期%'
                    GROUP BY ps.order_id
                ) overdue_agg ON o.id = overdue_agg.order_id
                WHERE o.status = '逾期'
                ORDER BY o.order_date DESC;
            """)
            
            # 执行查询
            query_start = time.time()
            result = session.execute(sql)
            rows = result.fetchall()
            query_time = time.time() - query_start
            logger.info(f"SQL查询完成，获得{len(rows)}条记录，耗时: {query_time:.4f}秒")
            
            # 转换为可序列化的结果
            results = []
            for row in rows:
                # 格式化日期
                order_date_str = row.order_date.strftime("%Y-%m-%d") if row.order_date else ''
                first_overdue_date_str = row.first_overdue_date.strftime("%Y-%m-%d") if row.first_overdue_date else ''
                
                # 客户信息
                customer_info = {
                    '手机号码': row.phone,
                    '客服': row.customer_service,
                    '业务': row.business_affiliation,
                    '客户信息备注': row.customer_remarks
                }
                
                # 按类别区分的数据结构
                order_data = {
                    # 订单基本信息
                    '订单日期': order_date_str,
                    '订单编号': row.order_number or '',
                    '客户姓名': row.customer_name or '',
                    '产品类型': row.product_type or '',
                    '型号': row.model or '',
                    '期数': row.periods or 0,
                    '台数': row.devices_count,
                    '成本': float(row.cost),
                    '备注': row.remarks,
                    
                    # 财务信息
                    '总待收': float(row.total_receivable),
                    '当前待收': float(row.current_receivable),
                    '逾期金额': round(float(row.overdue_amount), 2),
                    
                    # 逾期信息
                    '首次逾期日期': first_overdue_date_str,
                    '首次逾期期数': row.first_overdue_period or 0,
                    '逾期天数': row.first_overdue_days or 0,
                    '逾期期数': row.overdue_periods_count or 0,
                    
                    # 客户联系信息
                    '客户信息': customer_info
                }
                
                results.append(order_data)
            
            # 记录执行时间
            total_time = time.time() - start_time
            logger.info(f"逾期订单筛选查询完成（优化版本），找到{len(results)}条记录，总耗时: {total_time:.4f}秒")
            
            return results
        except Exception as e:
            logger.error(f"逾期订单筛选查询异常: {e}")
            raise
        finally:
            close_db_session(session)

    @classmethod
    def get_overdue_orders_data(cls, session=None):
        """
        获取逾期订单数据，包括各店铺的逾期订单统计 - 优化版本
        
        Args:
            session: 可选的数据库会话
            
        Returns:
            tuple: (逾期订单列表, 店铺逾期订单数量字典, 店铺逾期金额字典, 总逾期订单数, 总逾期金额)
        """
        start_time = time.time()
        logger.info("开始获取逾期订单数据统计（优化版本）")
        
        close_session = False
        if session is None:
            session = get_db_session()
            close_session = True
        
        try:
            # 使用原生SQL进行优化查询，一次性获取所有统计数据
            stats_sql = text("""
                SELECT 
                    COALESCE(o.shop_affiliation, '未分配') as shop,
                    COUNT(DISTINCT o.id) as order_count,
                    SUM(COALESCE(o.devices_count, 1)) as device_count,
                    COALESCE(SUM(overdue_agg.overdue_amount), 0) as total_overdue_amount
                FROM orders o
                LEFT JOIN (
                    SELECT 
                        ps.order_id,
                        -- 修复逾期金额计算：严格匹配原始逻辑
                        SUM(CASE 
                            WHEN ps.status IS NOT NULL 
                            AND ps.status LIKE '%逾期%'
                            AND COALESCE(ps.amount, 0) > COALESCE(ps.paid_amount, 0)
                            THEN COALESCE(ps.amount, 0) - COALESCE(ps.paid_amount, 0)
                            ELSE 0 
                        END) as overdue_amount
                    FROM payment_schedules ps 
                    WHERE ps.status IS NOT NULL AND ps.status LIKE '%逾期%'
                    GROUP BY ps.order_id
                ) overdue_agg ON o.id = overdue_agg.order_id
                WHERE o.status = '逾期'
                GROUP BY o.shop_affiliation
                ORDER BY shop;
            """)
            
            # 执行统计查询
            query_start = time.time()
            stats_result = session.execute(stats_sql)
            stats_rows = stats_result.fetchall()
            query_time = time.time() - query_start
            logger.info(f"统计查询完成，获得{len(stats_rows)}个店铺统计，耗时: {query_time:.4f}秒")
            
            # 初始化统计数据
            shop_overdue_counts = defaultdict(int)  # 各店铺逾期订单数量
            shop_overdue_amounts = defaultdict(float)  # 各店铺逾期金额
            total_overdue_count = 0  # 总逾期订单数
            total_overdue_amount = 0.0  # 总逾期金额
            
            # 处理统计结果
            for row in stats_rows:
                shop = row.shop
                device_count = int(row.device_count or 0)
                overdue_amount = float(row.total_overdue_amount or 0)
                
                shop_overdue_counts[shop] = device_count
                shop_overdue_amounts[shop] = overdue_amount
                total_overdue_count += device_count
                total_overdue_amount += overdue_amount
            
            # 获取逾期订单列表（如果需要详细数据）
            orders_sql = text("""
                SELECT o.id, o.order_number, o.order_date, o.customer_name,
                       o.shop_affiliation, o.devices_count
                FROM orders o
                WHERE o.status = '逾期'
                ORDER BY o.order_date DESC;
            """)
            
            orders_result = session.execute(orders_sql)
            order_rows = orders_result.fetchall()
            
            # 转换为Order对象列表（简化版，只包含必要字段）
            overdue_orders = []
            for row in order_rows:
                # 创建简化的订单对象，只包含统计需要的字段
                order_dict = {
                    'id': row.id,
                    'order_number': row.order_number,
                    'order_date': row.order_date,
                    'customer_name': row.customer_name,
                    'shop_affiliation': row.shop_affiliation,
                    'devices_count': row.devices_count or 1
                }
                overdue_orders.append(order_dict)
            
            # 记录执行时间
            total_time = time.time() - start_time
            logger.info(f"逾期订单数据统计完成（优化版本），共{len(overdue_orders)}条记录，总逾期订单数{total_overdue_count}，总逾期金额{total_overdue_amount:.2f}，总耗时: {total_time:.4f}秒")
            
            return (overdue_orders, shop_overdue_counts, shop_overdue_amounts, 
                    total_overdue_count, total_overdue_amount)
        
        except Exception as e:
            logger.error(f"获取逾期订单数据异常: {e}")
            raise
        finally:
            if close_session:
                close_db_session(session)
