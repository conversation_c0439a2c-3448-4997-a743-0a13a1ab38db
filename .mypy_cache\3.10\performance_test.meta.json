{"data_mtime": 1757727434, "dep_lines": [28, 16, 9, 10, 11, 12, 13, 14, 15, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app.routes.db.models", "sqlalchemy.orm", "time", "logging", "os", "sys", "json", "datetime", "sqlalchemy", "etl", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "app.routes.db", "enum", "io", "json.encoder", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.create", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.session", "sqlalchemy.pool", "sqlalchemy.pool.base", "sqlalchemy.sql", "sqlalchemy.sql.functions", "sqlalchemy.util", "sqlalchemy.util._py_collections", "typing", "typing_extensions"], "hash": "350677b113ea488e3982ef7d09ca33eafb7e7560", "id": "performance_test", "ignore_all": false, "interface_hash": "b334481cdedf0dcebfe3424d2a5e60d7fcd8b8e4", "mtime": 1757727430, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\fsdownload\\flask_api\\performance_test.py", "plugin_data": null, "size": 10520, "suppressed": [], "version_id": "1.15.0"}