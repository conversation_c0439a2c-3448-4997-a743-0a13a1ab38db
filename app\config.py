# app/config.py

import os
from dotenv import load_dotenv

load_dotenv()  # 加载 .env 文件

class Config:
    API_KEY = os.getenv('API_KEY', 'lxw8025031')
    LOG_FILE_PATH = os.getenv('LOG_FILE_PATH', '/www/server.log')
    
    # 数据库配置
    DATABASE_URI = os.getenv('DATABASE_URI', 'postgresql://flask_user:flask_password@localhost:5432/flask_db')
    
    # SQLAlchemy配置
    SQLALCHEMY_DATABASE_URI = DATABASE_URI
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,  # 连接池预检
        'pool_recycle': 300,    # 连接回收时间
    }

class DevelopmentConfig(Config):
    DEBUG = True
    TEMPLATES_AUTO_RELOAD = True
    SEND_FILE_MAX_AGE_DEFAULT = 0
    # 开发环境使用PostgreSQL（端口5433避免冲突）
    DATABASE_URI = os.getenv('DATABASE_URI', 'postgresql://flask_user:flask_password@localhost:5433/flask_db')
    SQLALCHEMY_DATABASE_URI = DATABASE_URI

class ProductionConfig(Config):
    DEBUG = False
    # 生产环境必须使用PostgreSQL
    DATABASE_URI = os.getenv('DATABASE_URI', '****************************************************/flask_db')
