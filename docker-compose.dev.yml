version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: flask-postgres-dev
    environment:
      POSTGRES_DB: flask_db_dev
      POSTGRES_USER: flask_user
      POSTGRES_PASSWORD: flask_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # 使用不同端口避免与生产环境冲突
    restart: unless-stopped
    networks:
      - flask-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U flask_user -d flask_db_dev"]
      interval: 5s
      timeout: 3s
      retries: 5

  flask-api:
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - DATABASE_URI=****************************************************/flask_db_dev
    volumes:
      # 开发模式下挂载源代码，支持热重载
      - ./app:/app/app
      - ./run.py:/app/run.py
      - ./scheduler.py:/app/scheduler.py
      - ./etl.py:/app/etl.py
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    command: ["python", "run.py"]
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - flask-network

networks:
  flask-network:
    driver: bridge

volumes:
  postgres_dev_data: 